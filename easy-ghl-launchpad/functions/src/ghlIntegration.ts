import * as functions from 'firebase-functions';
import axios from 'axios';
// import * as admin from 'firebase-admin'; // Uncomment if using Firebase Admin SDK for database

interface UserData { // Define the UserData interface
  company_name: string;
  phone: string;
  location: {
    address: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
  };
  website?: string;
  prospectInfo: {
    firstName: string;
    lastName: string;
    email: string;
  };
  // Add other properties if needed based on your form/frontend data
}

function isAxiosError(error: unknown): error is { response: { data: unknown } } {
  return (
    typeof error === 'object' &&
    error !== null &&
    'response' in error &&
    typeof (error as { response: unknown }).response === 'object' &&
    (error as { response: { data: unknown } }).response !== null &&
    'data' in (error as { response: { data: unknown } }).response
  );
}

// Define the expected structure of the request data
interface GhlIntegrationRequest {
  email: string;
  userData: UserData;
}

export const handleGhlIntegration = functions.https.onCall(async (request: functions.https.CallableRequest<GhlIntegrationRequest>) => {
  // Ensure the user is authenticated
  if (!request.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'The function must be called while authenticated.'
    );
  }

  const userEmail = request.data.email; // Access data property
  const userData = request.data.userData; // Access data property
  
  if (!userEmail || !userData) {
    throw new functions.https.HttpsError(
      'invalid-argument',
      'The function must be called with user email and data.'
    );
  }

  // TODO: Securely retrieve GHL Private Integration Key
  // Use Firebase Environment Configuration or Secret Manager
  const ghlPrivateKey = functions.config().ghl.private_key; // Example using Environment Config

  if (!ghlPrivateKey) {
      throw new functions.https.HttpsError(
          'internal',
          'Go High Level private key not configured.'
      );
  }

  console.log(`Handling GHL integration for user: ${userEmail}`);

  // 1. Make the "Create Sub-Account" API call
  const createSubaccountUrl = 'https://services.leadconnectorhq.com/locations/';
  const headers = {
    'Authorization': `Bearer ${ghlPrivateKey}`,
    'Version': '2021-07-28',
    'Content-Type': 'application/json',
  };

  // Construct the request body based on the N8N workflow and frontend data
  const createSubaccountBody = {
    name: userData.company_name,
    phone: userData.phone,
    companyId: 'gPkTndcx94O3r573TOMx', // Ensure this is your correct Company ID
    address: userData.location?.address,
    city: userData.location?.city,
    state: userData.location?.state,
    country: userData.location?.country,
    postalCode: userData.location?.postalCode,
    website: userData.website || '',
    timezone: 'US/Central',
    email: userEmail,
    prospectInfo: {
      firstName: userData.prospectInfo?.firstName,
      lastName: userData.prospectInfo?.lastName,
      email: userEmail,
    },
    settings: {
      allowDuplicateContact: false,
      allowDuplicateOpportunity: false,
      allowFacebookNameMerge: false,
      disableContactTimezone: false,
    },
    social: {
      facebookUrl: '',
      googlePlus: '',
      linkedIn: '',
      foursquare: '',
      twitter: '',
      yelp: '',
      instagram: '',
      youtube: '',
      pinterest: '',
      blogRss: '',
      googlePlacesId: '',
    },
  };

  try {
    const subaccountResponse = await axios.post(createSubaccountUrl, createSubaccountBody, { headers });
    const subaccountData = subaccountResponse.data;
    console.log('Create Sub-Account API Response:', subaccountData);

    const subaccountId = subaccountData.id;
    if (!subaccountId) {
        throw new functions.https.HttpsError(
            'internal',
            'Failed to get subaccount ID from GHL response.'
        );
    }

    // TODO: Implement "Create User" and "Enable SaaS" API calls using subaccountId

    // TODO: Store linked Firebase User ID (context.auth.uid) and GHL IDs in Firestore/Realtime Database


    // Placeholder success response - replace with actual success indicator after all steps are done    
    return { message: `Subaccount created successfully with ID: ${subaccountId}`, subaccountId: subaccountId };

  } catch (error: unknown) {
    if (isAxiosError(error)) {
      console.error('Error creating GHL subaccount:', error.response.data);
      throw new functions.https.HttpsError(
        'internal',
        'Failed to create GHL subaccount',
        error.response.data
      );
    } else if (error instanceof Error) {
      console.error('Error creating GHL subaccount:', error.message);
      throw new functions.https.HttpsError(
        'internal',
        'Failed to create GHL subaccount',
        error.message
      );
    } else {
      console.error('An unknown error occurred:', error);
      throw new functions.https.HttpsError(
        'internal',
        'An unknown error occurred.'
      );
    }
  }
});
