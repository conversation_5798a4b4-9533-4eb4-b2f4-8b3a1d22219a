/* CSS to fix white containers in GHL form */

/* Target the specific white containers on the sides */
.col-md-3, .col-lg-3, .col-xl-3, .col-3,
.col-sm-3, .col-xs-3,
.offset-md-3, .offset-lg-3, .offset-xl-3,
.offset-sm-3, .offset-xs-3,
div[class*="col-md-3"], div[class*="col-lg-3"], div[class*="col-xl-3"], div[class*="col-3"],
div[class*="col-sm-3"], div[class*="col-xs-3"],
div[class*="offset-md-3"], div[class*="offset-lg-3"], div[class*="offset-xl-3"],
div[class*="offset-sm-3"], div[class*="offset-xs-3"] {
  display: none !important;
  width: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  max-width: 0 !important;
  flex: 0 0 0% !important;
}

/* Make the center column take full width */
.col-md-6, .col-lg-6, .col-xl-6, .col-6,
.col-sm-6, .col-xs-6,
div[class*="col-md-6"], div[class*="col-lg-6"], div[class*="col-xl-6"], div[class*="col-6"],
div[class*="col-sm-6"], div[class*="col-xs-6"] {
  flex: 0 0 100% !important;
  max-width: 100% !important;
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Target any element with white background */
[style*="background-color: rgb(255, 255, 255)"],
[style*="background-color:#fff"],
[style*="background-color: #fff"],
[style*="background-color:white"],
[style*="background-color: white"],
[style*="background: white"],
[style*="background:white"],
[style*="background: #fff"],
[style*="background:#fff"],
[style*="background-color: rgb(248, 249, 250)"],
[style*="background-color:#f8f9fa"],
[style*="background-color: #f8f9fa"] {
  background-color: transparent !important;
  background: transparent !important;
}

/* Target the row container to ensure it takes full width */
.row, div[class*="row"] {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  display: flex !important;
  justify-content: center !important;
}

/* Target any white elements */
[style*="background-color: white"],
[style*="background: white"],
[style*="background-color: #fff"],
[style*="background: #fff"],
[style*="background-color: rgb(255, 255, 255)"],
[style*="background: rgb(255, 255, 255)"] {
  background-color: transparent !important;
  background: transparent !important;
}

/* Target specific elements that might be causing the white containers */
.form-box, .form-box-wrapper, .form-content, .form-wrapper, .form-group, .form-control,
.card, .card-body, .card-header, .card-footer, .modal, .modal-content, .modal-body, .modal-header, .modal-footer,
.container, .container-fluid, .row, .col, .col-md-6, .col-md-12 {
  background-color: transparent !important;
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
}

/* Target the container to ensure it takes full width */
.container, .container-fluid {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Force all elements to have transparent background */
* {
  background-color: transparent !important;
}

/* Ensure form inputs have proper styling */
input, select, textarea {
  background-color: rgba(26, 31, 44, 0.8) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  border-radius: 8px !important;
}

/* Style the submit button */
button, .btn, .btn-primary {
  background-color: #ea384c !important;
  border: none !important;
  color: white !important;
}
