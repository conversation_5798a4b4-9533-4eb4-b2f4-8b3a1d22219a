<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Direct GHL API</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input {
      width: 100%;
      padding: 8px;
      box-sizing: border-box;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      cursor: pointer;
    }
    #result {
      margin-top: 20px;
      padding: 15px;
      border: 1px solid #ddd;
      background-color: #f9f9f9;
      white-space: pre-wrap;
      display: none;
    }
  </style>
</head>
<body>
  <h1>Test Direct GHL API</h1>
  
  <div class="form-group">
    <label for="email">Email:</label>
    <input type="email" id="email" value="<EMAIL>">
  </div>
  
  <div class="form-group">
    <label for="firstName">First Name:</label>
    <input type="text" id="firstName" value="Test">
  </div>
  
  <div class="form-group">
    <label for="lastName">Last Name:</label>
    <input type="text" id="lastName" value="User">
  </div>
  
  <div class="form-group">
    <label for="companyName">Company Name:</label>
    <input type="text" id="companyName" value="Test Company">
  </div>
  
  <button id="testButton">Create GHL Location</button>
  
  <div id="result"></div>
  
  <script>
    // GHL API constants
    const GHL_API_URL = 'https://services.leadconnectorhq.com';
    const GHL_TOKEN = 'pit-a329e4c6-962c-4d43-9c41-01eb6933dbea';
    
    document.getElementById('testButton').addEventListener('click', async function() {
      const resultDiv = document.getElementById('result');
      resultDiv.style.display = 'block';
      resultDiv.textContent = 'Creating GHL location...';
      
      try {
        // Create a location in GHL
        const createResponse = await fetch(`${GHL_API_URL}/locations`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': GHL_TOKEN,
            'Version': '2021-07-28'
          },
          body: JSON.stringify({
            name: document.getElementById('companyName').value,
            email: document.getElementById('email').value,
            phone: '',
            address: {
              city: '',
              state: '',
              country: '',
            },
            timezone: 'America/New_York', // Default timezone
          }),
        });
        
        if (!createResponse.ok) {
          const errorText = await createResponse.text();
          throw new Error(`Failed to create GHL location: ${errorText}`);
        }
        
        const locationData = await createResponse.json();
        
        // Now create a user for this location
        resultDiv.textContent = 'Location created. Creating user...';
        
        const createUserResponse = await fetch(`${GHL_API_URL}/users`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': GHL_TOKEN,
            'Version': '2021-07-28'
          },
          body: JSON.stringify({
            email: document.getElementById('email').value,
            firstName: document.getElementById('firstName').value,
            lastName: document.getElementById('lastName').value,
            password: Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8),
            locationId: locationData.id,
            role: 'admin',
            type: 'account'
          }),
        });
        
        if (!createUserResponse.ok) {
          const errorText = await createUserResponse.text();
          throw new Error(`Failed to create GHL user: ${errorText}`);
        }
        
        const userData = await createUserResponse.json();
        
        // Show the final result
        resultDiv.textContent = JSON.stringify({
          success: true,
          location: locationData,
          user: userData
        }, null, 2);
      } catch (error) {
        resultDiv.textContent = 'Error: ' + error.message;
      }
    });
  </script>
</body>
</html>
