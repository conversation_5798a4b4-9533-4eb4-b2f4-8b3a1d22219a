Stack trace:
Frame         Function      Args
0007FFFF9F80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8E80) msys-2.0.dll+0x1FE8E
0007FFFF9F80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA258) msys-2.0.dll+0x67F9
0007FFFF9F80  000210046832 (000210286019, 0007FFFF9E38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9F80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9F80  000210068E24 (0007FFFF9F90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA260  00021006A225 (0007FFFF9F90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCAEA80000 ntdll.dll
7FFCAD1B0000 KERNEL32.DLL
7FFCABD90000 KERNELBASE.dll
7FFCAE600000 USER32.dll
7FFCAC490000 win32u.dll
7FFCAE440000 GDI32.dll
7FFCAC4C0000 gdi32full.dll
7FFCAC160000 msvcp_win.dll
000210040000 msys-2.0.dll
7FFCAC340000 ucrtbase.dll
7FFCACC60000 advapi32.dll
7FFCADBB0000 msvcrt.dll
7FFCAE910000 sechost.dll
7FFCAD280000 RPCRT4.dll
7FFCAB300000 CRYPTBASE.DLL
7FFCAC2A0000 bcryptPrimitives.dll
7FFCAE8D0000 IMM32.DLL
