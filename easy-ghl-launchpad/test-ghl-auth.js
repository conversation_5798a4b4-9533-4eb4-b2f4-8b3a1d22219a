import fetch from 'node-fetch';

// GHL API constants
const GHL_API_URL = 'https://services.leadconnectorhq.com';
const GHL_TOKEN = 'pit-a329e4c6-962c-4d43-9c41-01eb6933dbea';

async function testGHLAuth() {
  try {
    console.log('Testing GHL authentication...');
    
    // Try different authorization header formats
    const authFormats = [
      { name: 'Token as is', value: GHL_TOKEN },
      { name: 'Bearer token', value: `Bearer ${GHL_TOKEN}` },
      { name: 'Token with prefix', value: `Token ${GHL_TOKEN}` },
      { name: 'API key format', value: `ApiKey ${GHL_TOKEN}` }
    ];
    
    for (const auth of authFormats) {
      console.log(`\nTrying auth format: ${auth.name}`);
      
      const response = await fetch(`${GHL_API_URL}/api/v1/locations`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': auth.value,
          'Version': '2021-07-28'
        }
      });
      
      console.log('Response status:', response.status);
      console.log('Response status text:', response.statusText);
      
      const responseHeaders = {};
      response.headers.forEach((value, name) => {
        responseHeaders[name] = value;
      });
      console.log('Response headers:', JSON.stringify(responseHeaders, null, 2));
      
      const responseText = await response.text();
      console.log('Response body (truncated):', responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the test
testGHLAuth();
