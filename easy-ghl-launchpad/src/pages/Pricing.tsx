import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Tilt3D from '@/components/anim/Tilt3D';
import ModernHeader from '@/components/sections/ModernHeader';
import ModernFooter from '@/components/sections/ModernFooter';
import { ArrowRight, Check, Sparkles, DollarSign } from 'lucide-react';
import CompetitorLogos from '@/components/CompetitorLogos';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import '@/styles/pricing.css';

const Pricing = () => {
  const navigate = useNavigate();
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annual'>('annual');
  const heroRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  // Parallax effect for hero section
  const y = useTransform(scrollYProgress, [0, 1], [0, 200]);
  const opacity = useTransform(scrollYProgress, [0, 0.8], [1, 0]);

  // Define monthly and annual prices
  const prices = {
    basic: {
      monthly: '$15',
      annual: '$10.50',
      annualTotal: '$126'
    },
    agency: {
      monthly: '$59',
      annual: '$41',
      annualTotal: '$495'
    },
    enterprise: {
      monthly: '$79',
      annual: '$55.30',
      annualTotal: '$664'
    }
  };

  // Add a CSS class to ensure content remains visible
  useEffect(() => {
    // Add a class to the body to prevent content from fading
    document.body.classList.add('pricing-page');

    // Clean up when component unmounts
    return () => {
      document.body.classList.remove('pricing-page');
    };
  }, []);

  return (
    <div className="min-h-screen w-full text-white overflow-x-hidden pricing-content" style={{ background: '#0F0F0F' }}>
      <ModernHeader />

      {/* Hero Section */}
      <section ref={heroRef} className="py-16 relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #0F0F0F 0%, rgba(144, 25, 28, 0.8) 100%)' }}>
        {/* Background elements */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-4xl md:text-6xl font-bold text-center mb-16"
          >
            Pricing
          </motion.h1>

          <div className="max-w-5xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-2xl md:text-4xl font-bold text-aha-red text-center mb-8"
            >
              Save Thousands: All-In-One Solution vs Individual Subscriptions
            </motion.h2>

            {/* Comparison Table */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="overflow-x-auto mb-16 glass-card rounded-xl p-1 pricing-table"
            >
              <Table className="w-full">
                <TableHeader>
                  <TableRow className="bg-gradient-to-r from-gray-800/30 to-gray-800/40 backdrop-blur-sm">
                    <TableHead className="text-white font-bold text-lg py-4">Feature</TableHead>
                    <TableHead className="text-white text-center font-bold text-lg py-4">Competitor</TableHead>
                    <TableHead className="text-white text-center font-bold text-lg py-4">Price</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <ComparisonRow
                    feature="CRM & Pipeline Management"
                    competitor="HubSpot"
                    price="$99/month"
                    index={0}
                  />
                  <ComparisonRow
                    feature="Unlimited Sales Funnels"
                    competitor="ClickFunnels"
                    price="$297/month"
                    index={1}
                  />
                  <ComparisonRow
                    feature="Surveys & Forms"
                    competitor="JotForm"
                    price="$29/month"
                    index={2}
                  />
                  <ComparisonRow
                    feature="Email Marketing"
                    competitor="Mailchimp"
                    price="$49/month"
                    index={3}
                  />
                  <ComparisonRow
                    feature="2-Way SMS Marketing"
                    competitor="Twilio"
                    price="$99/month"
                    index={4}
                  />
                  <ComparisonRow
                    feature="Website Builder"
                    competitor="Wix"
                    price="$99/month"
                    index={5}
                  />
                  <ComparisonRow
                    feature="Booking & Appointments"
                    competitor="Calendly"
                    price="$29/month"
                    index={6}
                  />
                  <ComparisonRow
                    feature="Workflow Automations"
                    competitor="Zapier"
                    price="$169/month"
                    index={7}
                  />
                  <ComparisonRow
                    feature="Courses/Products"
                    competitor="Kajabi"
                    price="$99/month"
                    index={8}
                  />
                </TableBody>
              </Table>
            </motion.div>

            {/* Pricing Comparison */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="glass-card rounded-xl p-8 mb-16 relative overflow-hidden glow-effect"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-aha-red/10 to-aha-red/20 opacity-80 z-0"></div>
              <div className="relative z-10">
                <div className="flex flex-col md:flex-row items-center justify-between gap-8">
                  <motion.div
                    className="text-center md:text-left"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <div className="flex items-center mb-4">
                      <Sparkles className="text-yellow-300 mr-2" />
                      <h3 className="text-yellow-300 text-2xl font-bold">Aha-Innovations:</h3>
                    </div>
                    <div className="glass-card bg-black/20 p-6 rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300">
                      <motion.div
                        className="text-white text-4xl font-bold mb-2"
                        initial={{ scale: 0.9 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.5, delay: 0.8 }}
                      >
                        Only $55.30/month
                      </motion.div>
                      <div className="text-white/90">billed annually</div>
                      <div className="text-white/90 mt-2">or $79/month billed monthly</div>
                      <motion.div
                        className="mt-4 bg-gradient-to-r from-aha-red/80 to-aha-darkred/80 rounded-lg p-2 text-white text-sm"
                        whileHover={{ scale: 1.05 }}
                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                      >
                        Save over $6,900 per month!
                      </motion.div>
                    </div>
                  </motion.div>

                  <motion.div
                    className="text-center relative"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <div className="w-16 h-16 rounded-full bg-gradient-to-br from-white/10 to-white/5 flex items-center justify-center text-3xl font-bold shadow-lg">
                      VS
                    </div>
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 rounded-full bg-white/5 animate-pulse"></div>
                  </motion.div>

                  <motion.div
                    className="text-center md:text-right"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <h3 className="text-white text-2xl font-bold mb-4">Total Competitor Pricing:</h3>
                    <div className="glass-card bg-black/20 p-6 rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300">
                      <motion.div
                        className="text-white text-4xl font-bold"
                        initial={{ scale: 0.9 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.5, delay: 0.8 }}
                      >
                        $7,000+/month
                      </motion.div>
                      <div className="text-white/90">for all individual services</div>
                      <div className="flex flex-wrap justify-center md:justify-end gap-3 mt-4">
                        <motion.div whileHover={{ scale: 1.1 }} transition={{ type: "spring", stiffness: 400, damping: 10 }}>
                          <CompetitorLogos name="HubSpot" />
                        </motion.div>
                        <motion.div whileHover={{ scale: 1.1 }} transition={{ type: "spring", stiffness: 400, damping: 10 }}>
                          <CompetitorLogos name="ClickFunnels" />
                        </motion.div>
                        <motion.div whileHover={{ scale: 1.1 }} transition={{ type: "spring", stiffness: 400, damping: 10 }}>
                          <CompetitorLogos name="Mailchimp" />
                        </motion.div>
                        <motion.div whileHover={{ scale: 1.1 }} transition={{ type: "spring", stiffness: 400, damping: 10 }}>
                          <CompetitorLogos name="Zapier" />
                        </motion.div>
                      </div>
                    </div>
                  </motion.div>
                </div>
              </div>
            </motion.div>

            {/* Pricing Plans */}
            <div className="mt-8">
              {/* Billing Toggle */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="flex justify-center mb-6"
              >
                <div className="toggle-switch">
                  <div
                    className="slider"
                    style={{
                      left: billingCycle === 'monthly' ? '3px' : '50%',
                      width: 'calc(50% - 6px)',
                      background: billingCycle === 'monthly' ? 'rgba(50, 50, 60, 0.8)' : 'rgba(234, 56, 76, 0.9)'
                    }}
                  ></div>
                  <button
                    className={`transition-all duration-300 ${billingCycle === 'monthly' ? 'active' : 'text-gray-300 hover:text-white'}`}
                    onClick={() => setBillingCycle('monthly')}
                  >
                    Monthly
                  </button>
                  <button
                    className={`transition-all duration-300 ${billingCycle === 'annual' ? 'active' : 'text-gray-300 hover:text-white'}`}
                    onClick={() => setBillingCycle('annual')}
                  >
                    Annual
                  </button>
                </div>
              </motion.div>

              {/* Discount Alert */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="glass-card bg-gradient-to-r from-aha-red/80 to-aha-darkred/80 rounded-lg p-4 mb-8 text-center"
              >
                <span className="text-yellow-300 text-lg font-bold flex items-center justify-center">
                  <Sparkles className="w-5 h-5 mr-2 text-yellow-300" />
                  Enjoy 30% Discount on Annual Plans!
                  <Sparkles className="w-5 h-5 ml-2 text-yellow-300" />
                </span>
              </motion.div>

              {/* Pricing Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <PricingCard
                  title="Basic Plan"
                  price={billingCycle === 'monthly' ? prices.basic.monthly : prices.basic.annual}
                  billingCycle={billingCycle}
                  annualPrice={prices.basic.annualTotal}
                  label="PERFECT FOR FREELANCERS"
                  features={[
                    "15 Pipelines",
                    "Unlimited Sales Funnel",
                    "1,000 Email Marketing",
                    "Unlimited Contacts"
                  ]}
                />

                <PricingCard
                  title="Agency Plan"
                  price={billingCycle === 'monthly' ? prices.agency.monthly : prices.agency.annual}
                  billingCycle={billingCycle}
                  annualPrice={prices.agency.annualTotal}
                  label="IDEAL FOR SMALL AGENCIES"
                  isBestSeller
                  features={[
                    "50 Pipelines",
                    "Unlimited Sales Funnel",
                    "7,500 Email Marketing",
                    "Workflow Triggers & Actions"
                  ]}
                />

                <PricingCard
                  title="Enterprise Plan"
                  price={billingCycle === 'monthly' ? prices.enterprise.monthly : prices.enterprise.annual}
                  billingCycle={billingCycle}
                  annualPrice={prices.enterprise.annualTotal}
                  label="RECOMMENDED FOR BUSINESS"
                  features={[
                    "Unlimited Pipelines",
                    "Unlimited Sales Funnel",
                    "15,000 Email Marketing",
                    "Reputation Management"
                  ]}
                />
              </div>

              {/* 14-Day Trial CTA Section */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true, margin: "-50px" }}
                className="mt-20 mb-12 max-w-4xl mx-auto"
              >
                <div className="glass-card rounded-xl overflow-hidden border border-white/10 glow-effect">
                  <div className="bg-gradient-to-r from-aha-red/20 to-aha-darkred/20 p-10 text-center relative">
                    {/* Background glow effect */}
                    <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(234,56,76,0.15),transparent_70%)]"></div>

                    <div className="relative z-10">
                      <motion.div
                        initial={{ scale: 0.9 }}
                        whileInView={{ scale: 1 }}
                        transition={{ duration: 0.5, delay: 0.1 }}
                        viewport={{ once: true }}
                      >
                        <h2 className="text-3xl md:text-4xl font-bold mb-4">Try AHA-Innovations Free for 14 Days</h2>
                      </motion.div>

                      <motion.p
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                        viewport={{ once: true }}
                        className="text-lg text-white/90 mb-8 max-w-2xl mx-auto"
                      >
                        Experience all features with no limitations. No credit card required to start.
                        Upgrade to any plan when you're ready to scale your business.
                      </motion.p>

                      <motion.div
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="inline-block"
                      >
                        <Button
                          onClick={() => navigate('/signup')}
                          size="lg"
                          className="bg-aha-red hover:bg-aha-darkred text-white text-lg px-8 py-6 group relative overflow-hidden"
                        >
                          <span className="relative z-10 flex items-center gap-2">
                            Start Your 14-Day Free Trial
                            <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                          </span>
                          <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
                        </Button>
                      </motion.div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      <ModernFooter />
    </div>
  );
};

// Helper Components
const ComparisonRow = ({ feature, competitor, price, index }) => {
  return (
    <TableRow className="border-b border-white/10 hover:bg-white/5 transition-colors">
      <TableCell className="py-4 font-medium">
        <motion.div
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 * index }}
          className="flex items-center"
        >
          <Check className="text-aha-red mr-2 h-5 w-5" />
          {feature}
        </motion.div>
      </TableCell>
      <TableCell className="text-center py-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.1 * index + 0.1 }}
          className="flex justify-center"
        >
          <CompetitorLogos name={competitor} />
        </motion.div>
      </TableCell>
      <TableCell className="text-center py-4 font-bold">
        <motion.div
          initial={{ opacity: 0, x: 10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 * index + 0.2 }}
        >
          {price}
        </motion.div>
      </TableCell>
    </TableRow>
  );
};

const PricingCard = ({
  title,
  price,
  billingCycle,
  annualPrice,
  label,
  features = [],
  isBestSeller = false,
  custom = false,
  customDescription = ''
}) => {
  // Calculate animation delay based on index for staggered animation
  const getAnimationDelay = (index) => `${0.1 + (index * 0.1)}s`;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: isBestSeller ? 0.2 : 0.3 }}
      className={`relative ${isBestSeller ? 'transform scale-105 z-10' : ''}`}
    >
      <Tilt3D className="h-full">
        <div className={`relative rounded-xl overflow-hidden h-full glass-card border border-white/10 pricing-card glow-effect ${isBestSeller ? 'float-animation' : ''}`}>
          {isBestSeller && (
            <div className="absolute top-0 left-0 right-0 z-20">
              <div className="best-seller-banner py-2 px-4 font-bold text-center text-sm">
                BEST SELLER!
              </div>
            </div>
          )}

          <div className={`bg-gradient-to-br from-aha-red/90 to-aha-darkred/90 p-6 text-center relative z-10 ${isBestSeller ? 'pt-12' : ''}`}>
            <h3 className="text-2xl font-bold mb-4">{title}</h3>
            <div className="text-5xl font-bold mb-2">{price}</div>
            <div className="text-sm mb-4 text-white/90">Price per month</div>
            {billingCycle === 'annual' && (
              <div className="text-yellow-300 font-bold mb-4 flex items-center justify-center">
                <DollarSign className="w-4 h-4 mr-1" />
                {annualPrice} billed annually
              </div>
            )}
          </div>

          <div className="bg-black/20 p-6 backdrop-blur-sm h-full flex flex-col">
            <div className="glass-card bg-white/10 text-center py-2 mb-6 rounded-lg">
              <span className="text-sm font-bold">{label}</span>
            </div>

            <ul className="space-y-4 mb-6 flex-grow">
              {features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <div className="bg-aha-red/20 rounded-full p-1 mr-2 mt-0.5">
                    <Check className="w-3 h-3 text-aha-red" />
                  </div>
                  <span>{feature}</span>
                </li>
              ))}
            </ul>

            <Button className="w-full bg-aha-red hover:bg-aha-darkred relative group overflow-hidden pricing-button">
              <span className="relative z-10">START FOR FREE</span>
            </Button>
          </div>
        </div>
      </Tilt3D>
    </motion.div>
  );
};

export default Pricing;
