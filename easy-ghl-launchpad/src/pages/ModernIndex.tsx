import React from 'react';
import ModernHeader from '../components/sections/ModernHeader';
import ModernHero from '../components/sections/ModernHero';
import PainPoints from '../components/sections/PainPoints';
import ModernFeatures from '../components/sections/ModernFeatures';
import ServicesSection from '../components/sections/ServicesSection';
import ModernShowcase from '../components/sections/ModernShowcase';
import CustomerShowcase from '../components/sections/CustomerShowcase';
import FeatureShowcase from '../components/sections/FeatureShowcase';
import TrustedCompanies from '../components/sections/TrustedCompanies';
import EnhancedTestimonials from '../components/sections/EnhancedTestimonials';
import ResourceHub from '../components/sections/ResourceHub';
import ModernCTA from '../components/sections/ModernCTA';
import ModernFooter from '../components/sections/ModernFooter';

const ModernIndex: React.FC = () => {
  return (
    <div className="min-h-screen w-full text-white overflow-x-hidden" style={{ background: '#0F0F0F' }}>
      <ModernHeader />

      <ModernHero
        title="Platform"
        subtitle="Want to build your custom solution? Click here"
        description="AHA-Innovations provides a comprehensive platform for businesses to manage clients, automate workflows, build sales funnels, and scale their operations with powerful tools and AI-driven insights."
        ctaText="Get Started Free"
        ctaLink="/signup"
      />

      <PainPoints />

      <ModernFeatures />

      <ServicesSection />

      <TrustedCompanies />

      <ModernShowcase />

      <EnhancedTestimonials />

      <ResourceHub />

      <ModernCTA />

      <ModernFooter />
    </div>
  );
};

export default ModernIndex;
