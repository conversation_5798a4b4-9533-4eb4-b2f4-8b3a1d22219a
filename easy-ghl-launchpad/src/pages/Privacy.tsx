import React, { useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import ModernHeader from '@/components/sections/ModernHeader';
import ModernFooter from '@/components/sections/ModernFooter';

const Privacy = () => {
  const heroRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  // Parallax effect for hero section
  const y = useTransform(scrollYProgress, [0, 1], [0, 200]);
  const opacity = useTransform(scrollYProgress, [0, 0.8], [1, 0]);

  return (
    <div className="min-h-screen w-full text-white overflow-x-hidden" style={{ background: '#0F0F0F' }}>
      <ModernHeader />

      {/* Hero Section */}
      <section ref={heroRef} className="py-16 relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #0F0F0F 0%, rgba(144, 25, 28, 0.8) 100%)' }}>
        {/* Background elements */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>
        </div>

        <motion.div
          style={{ y, opacity }}
          className="container mx-auto px-4 pt-20 pb-16 relative z-10"
        >
          <div className="text-center max-w-3xl mx-auto">
            <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-1.5 px-4 mx-auto block w-fit">
              Legal
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Privacy Policy
            </h1>
            <p className="text-xl text-gray-300">
              Last updated: {new Date().toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}
            </p>
          </div>
        </motion.div>
      </section>

      {/* Privacy Content */}
      <section className="py-20 relative">
        <div className="container mx-auto px-4">
          <div className="glass-card rounded-xl p-8 border border-white/10 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-aha-red/5 to-transparent opacity-30"></div>
            <div className="relative z-10 prose prose-invert max-w-none">
              <h2>1. Introduction</h2>
              <p>
                At AHA-Innovations, we respect your privacy and are committed to protecting your personal data. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our service.
              </p>

              <h2>2. Information We Collect</h2>
              <p>
                We collect several types of information from and about users of our Service, including:
              </p>
              <ul>
                <li>Personal identifiers such as name, email address, and phone number</li>
                <li>Account credentials</li>
                <li>Payment information</li>
                <li>Usage data and analytics</li>
                <li>Device and connection information</li>
              </ul>

              <h2>3. How We Use Your Information</h2>
              <p>
                We use the information we collect about you for various purposes, including:
              </p>
              <ul>
                <li>Providing, maintaining, and improving our Service</li>
                <li>Processing transactions and managing your account</li>
                <li>Responding to your requests and inquiries</li>
                <li>Sending administrative information and service updates</li>
                <li>Marketing and promotional communications (with your consent)</li>
                <li>Analyzing usage patterns to enhance user experience</li>
                <li>Protecting our rights and preventing fraud</li>
              </ul>

              <h2>4. Disclosure of Your Information</h2>
              <p>
                We may disclose your personal information in the following circumstances:
              </p>
              <ul>
                <li>To our subsidiaries and affiliates</li>
                <li>To contractors, service providers, and other third parties we use to support our business</li>
                <li>To comply with legal obligations</li>
                <li>To enforce our terms of service</li>
                <li>In connection with a business transfer or acquisition</li>
                <li>With your consent</li>
              </ul>

              <h2>5. Data Security</h2>
              <p>
                We implement appropriate security measures to protect your personal information from unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the Internet or electronic storage is 100% secure, and we cannot guarantee absolute security.
              </p>

              <h2>6. Your Data Protection Rights</h2>
              <p>
                Depending on your location, you may have certain rights regarding your personal information, including:
              </p>
              <ul>
                <li>The right to access your personal data</li>
                <li>The right to rectification of inaccurate data</li>
                <li>The right to erasure of your data</li>
                <li>The right to restrict processing</li>
                <li>The right to data portability</li>
                <li>The right to object to processing</li>
              </ul>

              <h2>7. Children's Privacy</h2>
              <p>
                Our Service is not intended for children under 16 years of age, and we do not knowingly collect personal information from children under 16.
              </p>

              <h2>8. Changes to Our Privacy Policy</h2>
              <p>
                We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last updated" date.
              </p>

              <h2>9. Contact Us</h2>
              <p>
                If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.
              </p>
            </div>
          </div>
        </div>
      </section>

      <ModernFooter />
    </div>
  );
};

export default Privacy;
