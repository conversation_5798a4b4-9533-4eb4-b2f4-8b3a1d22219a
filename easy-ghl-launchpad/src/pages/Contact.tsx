import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, useScroll, useTransform } from 'framer-motion';
import Logo from '@/components/Logo';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { Upload, Send, ArrowRight, Mail, Phone, MapPin } from 'lucide-react';
import Tilt3D from '@/components/anim/Tilt3D';
import ModernHeader from '@/components/sections/ModernHeader';
import ModernFooter from '@/components/sections/ModernFooter';

const Contact = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    problem: '',
  });
  const [screenshot, setScreenshot] = useState<File | null>(null);

  const heroRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  // Parallax effect for hero section
  const y = useTransform(scrollYProgress, [0, 1], [0, 200]);
  const opacity = useTransform(scrollYProgress, [0, 0.8], [1, 0]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setScreenshot(e.target.files[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Form validation
    if (!formData.fullName || !formData.email || !formData.problem) {
      toast({
        title: "Error",
        description: "Please fill out all required fields",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    // Simulate form submission
    setTimeout(() => {
      toast({
        title: "Success!",
        description: "Your support ticket has been submitted. We'll get back to you soon.",
      });
      setIsSubmitting(false);
      setFormData({ fullName: '', email: '', problem: '' });
      setScreenshot(null);
    }, 1500);
  };

  return (
    <div className="min-h-screen w-full text-white overflow-x-hidden" style={{ background: '#0F0F0F' }}>
      <ModernHeader />

      {/* Contact Section */}
      <section ref={heroRef} className="py-16 relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #0F0F0F 0%, rgba(144, 25, 28, 0.8) 100%)' }}>
        {/* Background elements */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>
        </div>

        <motion.div style={{ y, opacity }} className="container mx-auto px-4 relative z-10">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-4xl md:text-6xl font-bold text-center mb-16"
          >
            Contact
          </motion.h1>

          <div className="max-w-5xl mx-auto">
            <div className="grid md:grid-cols-3 gap-12 mb-16">
              <div className="col-span-2">
                <motion.h2
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="text-3xl font-bold mb-4"
                >
                  Get in Touch
                </motion.h2>
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="mb-4 text-gray-300"
                >
                  How to contact us
                </motion.p>
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="mb-8 text-gray-300"
                >
                  Complete the form below to contact us and generate a ticket
                </motion.p>

                {/* Contact Form */}
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                >
                  <Card className="glass-card border border-gray-800/50 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-aha-red/5 to-transparent opacity-50"></div>
                    <CardHeader className="relative z-10">
                      <CardTitle className="text-2xl text-center">AHA Support Ticket</CardTitle>
                    </CardHeader>
                    <CardContent className="relative z-10">
                      <form onSubmit={handleSubmit} className="space-y-6">
                        <div>
                          <label htmlFor="fullName" className="block mb-2">Full Name</label>
                          <Input
                            id="fullName"
                            name="fullName"
                            placeholder="Full Name"
                            value={formData.fullName}
                            onChange={handleChange}
                            className="bg-gray-900/50 backdrop-blur-sm border-gray-700 focus:border-aha-red/50 transition-colors duration-300"
                            required
                          />
                        </div>
                        <div>
                          <label htmlFor="email" className="block mb-2">Email <span className="text-aha-red">*</span></label>
                          <Input
                            id="email"
                            name="email"
                            type="email"
                            placeholder="Email"
                            value={formData.email}
                            onChange={handleChange}
                            className="bg-gray-900/50 backdrop-blur-sm border-gray-700 focus:border-aha-red/50 transition-colors duration-300"
                            required
                          />
                        </div>
                        <div>
                          <label htmlFor="problem" className="block mb-2">Problem <span className="text-aha-red">*</span></label>
                          <Textarea
                            id="problem"
                            name="problem"
                            placeholder="I have a problem with..."
                            value={formData.problem}
                            onChange={handleChange}
                            className="bg-gray-900/50 backdrop-blur-sm border-gray-700 focus:border-aha-red/50 transition-colors duration-300 min-h-[100px]"
                            required
                          />
                          <p className="text-xs text-gray-400 mt-1">Please write a detailed description of your problem.</p>
                        </div>
                        <div>
                          <label htmlFor="screenshot" className="block mb-2">Screenshot</label>
                          <div className="border border-dashed border-gray-600 rounded-lg p-4 text-center hover:border-aha-red/50 transition-colors duration-300">
                            <label htmlFor="screenshot-upload" className="cursor-pointer flex flex-col items-center justify-center py-6">
                              <Upload className="h-10 w-10 text-gray-400 mb-2 group-hover:text-aha-red transition-colors duration-300" />
                              <span className="text-gray-400">Upload Screenshot</span>
                              <input
                                id="screenshot-upload"
                                type="file"
                                accept="image/*"
                                onChange={handleFileChange}
                                className="hidden"
                              />
                              {screenshot && (
                                <p className="mt-2 text-sm text-green-500">File selected: {screenshot.name}</p>
                              )}
                            </label>
                          </div>
                          <p className="text-xs text-gray-400 mt-1">If possible (or relevant) please add a screenshot.</p>
                        </div>
                        <Button
                          type="submit"
                          className="w-full bg-aha-red hover:bg-aha-darkred group relative overflow-hidden"
                          disabled={isSubmitting}
                        >
                          <span className="relative z-10 flex items-center justify-center">
                            {isSubmitting ? "Submitting..." : "Submit"}
                            {!isSubmitting && <Send className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />}
                          </span>
                          <span className="absolute inset-0 bg-white/20 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300"></span>
                        </Button>
                      </form>
                    </CardContent>
                  </Card>
                </motion.div>
              </div>

              <div>
                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                  className="space-y-8"
                >
                  <Tilt3D intensity={5}>
                    <div className="flex justify-center mb-6">
                      <div className="w-32 h-32 bg-aha-red/80 backdrop-blur-sm rounded-lg flex items-center justify-center relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:shadow-aha-red/20">
                        <div className="absolute inset-0 bg-gradient-to-br from-aha-red to-aha-darkred opacity-80"></div>
                        <div className="relative z-10">
                          <img
                            src="/AHA-logomodern.png"
                            alt="AHA Innovations"
                            className="h-16 w-auto"
                          />
                        </div>
                      </div>
                    </div>
                  </Tilt3D>

                  <Tilt3D intensity={5}>
                    <div className="glass-card rounded-xl p-6 transition-all duration-300 hover:shadow-lg">
                      <div className="flex items-start">
                        <div className="bg-aha-red/80 backdrop-blur-sm p-3 rounded-full mr-4">
                          <Mail className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h4 className="text-xl font-bold mb-2">Email Us</h4>
                          <p className="text-gray-300 mb-1"><EMAIL></p>
                        </div>
                      </div>
                    </div>
                  </Tilt3D>

                  <Tilt3D intensity={5}>
                    <div className="glass-card rounded-xl p-6 transition-all duration-300 hover:shadow-lg">
                      <div className="flex items-start">
                        <div className="bg-aha-red/80 backdrop-blur-sm p-3 rounded-full mr-4">
                          <Phone className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h4 className="text-xl font-bold mb-2">Call Us</h4>
                          <p className="text-gray-300 mb-1">+****************</p>
                        </div>
                      </div>
                    </div>
                  </Tilt3D>
                </motion.div>
              </div>
            </div>

            {/* Location Section */}
            <div>
              <h2 className="text-3xl font-bold mb-6">Our Location</h2>
              <p className="mb-8 text-gray-300">Visit us to explore innovative solutions today.</p>

              <div className="grid md:grid-cols-2 gap-8 mb-8">
                <div>
                  <h3 className="text-xl font-bold mb-4">Get In Touch</h3>
                  <p className="text-gray-300">6921, 1021 E Lincolnway, Cheyenne, Wyoming, United States</p>
                </div>

                <div>
                  <h3 className="text-xl font-bold mb-4">Contact Us</h3>
                  <p className="text-gray-300">Monday – Friday 8:00 a.m. – 5:00 p.m.</p>
                </div>
              </div>

              {/* Google Map */}
              <div className="border border-gray-700 rounded-lg overflow-hidden">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3014.8696474487305!2d-72.58243812352329!3d42.14982804776801!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89e6dc14c61a7d57%3A0x3e120e65458b678c!2sHilltop%20St%2C%20Springfield%2C%20MA%2001128!5e0!3m2!1sen!2sus!4v1701286737151!5m2!1sen!2sus"
                  width="100%"
                  height="300"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade" />
              </div>
            </div>
          </div>
        </motion.div>
      </section>

      <ModernFooter />
    </div>
  );
};

export default Contact;
