import React, { useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Shield, Lock, Server, AlertTriangle, CheckCircle } from 'lucide-react';
import ModernHeader from '@/components/sections/ModernHeader';
import ModernFooter from '@/components/sections/ModernFooter';
import Tilt3D from '@/components/anim/Tilt3D';

const Security = () => {
  const heroRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  // Parallax effect for hero section
  const y = useTransform(scrollYProgress, [0, 1], [0, 200]);
  const opacity = useTransform(scrollYProgress, [0, 0.8], [1, 0]);

  return (
    <div className="min-h-screen w-full text-white overflow-x-hidden" style={{ background: '#0F0F0F' }}>
      <ModernHeader />

      {/* Hero Section */}
      <section ref={heroRef} className="py-16 relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #0F0F0F 0%, rgba(144, 25, 28, 0.8) 100%)' }}>
        {/* Background elements */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>
        </div>

        <motion.div
          style={{ y, opacity }}
          className="container mx-auto px-4 pt-20 pb-16 relative z-10"
        >
          <div className="text-center max-w-3xl mx-auto">
            <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-1.5 px-4 mx-auto block w-fit">
              Security
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Our Security Commitment
            </h1>
            <p className="text-xl text-gray-300">
              How we protect your data and ensure the security of our platform
            </p>
          </div>
        </motion.div>
      </section>

      {/* Security Overview */}
      <section className="py-20 relative">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Your Security Is Our Priority
            </h2>
            <p className="text-xl text-gray-300">
              At AHA-Innovations, we implement industry-leading security measures to protect your data and ensure the integrity of our platform.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <div className="glass-card rounded-xl p-8 border border-white/10 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-aha-red/5 to-transparent opacity-30"></div>
                <div className="relative z-10 prose prose-invert max-w-none">
                  <h3 className="text-2xl font-bold mb-4">Our Security Framework</h3>
                  <p>
                    AHA-Innovations employs a comprehensive security framework that includes:
                  </p>
                  <ul>
                    <li>End-to-end encryption for all data in transit and at rest</li>
                    <li>Regular security audits and penetration testing</li>
                    <li>Multi-factor authentication</li>
                    <li>Role-based access controls</li>
                    <li>Continuous monitoring for suspicious activities</li>
                    <li>Compliance with industry standards and regulations</li>
                  </ul>
                </div>
              </div>
            </motion.div>

            <div className="grid grid-cols-2 gap-6">
              <SecurityFeatureCard 
                icon={<Shield className="h-8 w-8 text-aha-red" />}
                title="Data Protection"
                description="Your data is encrypted both in transit and at rest using industry-standard encryption protocols."
              />
              <SecurityFeatureCard 
                icon={<Lock className="h-8 w-8 text-aha-red" />}
                title="Access Control"
                description="Strict access controls ensure only authorized personnel can access sensitive information."
              />
              <SecurityFeatureCard 
                icon={<Server className="h-8 w-8 text-aha-red" />}
                title="Infrastructure"
                description="Our infrastructure is hosted in secure, SOC 2 compliant data centers with redundancy."
              />
              <SecurityFeatureCard 
                icon={<AlertTriangle className="h-8 w-8 text-aha-red" />}
                title="Incident Response"
                description="We have robust incident response procedures to address security events promptly."
              />
            </div>
          </div>
        </div>
      </section>

      {/* Security Practices */}
      <section className="py-20 relative bg-black/30">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-1.5 px-4 mx-auto block w-fit">
              Best Practices
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Our Security Practices
            </h2>
            <p className="text-xl text-gray-300">
              We implement the following security measures to protect your data
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <SecurityPracticeCard
              title="Data Encryption"
              description="All data is encrypted using AES-256 encryption, both in transit and at rest, ensuring your information remains secure."
            />
            <SecurityPracticeCard
              title="Regular Security Audits"
              description="We conduct regular security audits and penetration testing to identify and address potential vulnerabilities."
            />
            <SecurityPracticeCard
              title="Multi-Factor Authentication"
              description="We support multi-factor authentication to add an extra layer of security to your account."
            />
            <SecurityPracticeCard
              title="Secure Development"
              description="Our development process follows secure coding practices and includes regular code reviews and security testing."
            />
            <SecurityPracticeCard
              title="Compliance"
              description="We maintain compliance with industry standards and regulations, including GDPR, CCPA, and SOC 2."
            />
            <SecurityPracticeCard
              title="Backup & Recovery"
              description="Regular backups and disaster recovery procedures ensure your data is safe and can be restored if needed."
            />
          </div>
        </div>
      </section>

      {/* Reporting Security Issues */}
      <section className="py-20 relative">
        <div className="container mx-auto px-4">
          <div className="glass-card rounded-xl p-8 border border-white/10 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-aha-red/10 to-transparent"></div>
            <div className="relative z-10 text-center max-w-3xl mx-auto">
              <h2 className="text-3xl font-bold mb-6">
                Reporting Security Issues
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                If you discover a potential security issue, please report it to us <NAME_EMAIL>. We appreciate your help in keeping our platform secure.
              </p>
              <div className="flex items-center justify-center gap-2 text-green-400">
                <CheckCircle className="h-5 w-5" />
                <span>We have a responsible disclosure policy and do not pursue legal action against security researchers.</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <ModernFooter />
    </div>
  );
};

// Security Feature Card Component
const SecurityFeatureCard = ({ icon, title, description }: { icon: React.ReactNode; title: string; description: string }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6 }}
    >
      <Tilt3D intensity={5}>
        <div className="glass-card rounded-xl p-6 border border-white/10 h-full relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-aha-red/5 to-transparent opacity-30"></div>
          <div className="relative z-10">
            <div className="mb-4">{icon}</div>
            <h3 className="text-xl font-bold mb-2">{title}</h3>
            <p className="text-gray-300 text-sm">{description}</p>
          </div>
        </div>
      </Tilt3D>
    </motion.div>
  );
};

// Security Practice Card Component
const SecurityPracticeCard = ({ title, description }: { title: string; description: string }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6 }}
    >
      <Tilt3D intensity={5}>
        <div className="glass-card rounded-xl p-8 border border-white/10 h-full relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-aha-red/5 to-transparent opacity-30"></div>
          <div className="relative z-10">
            <h3 className="text-xl font-bold mb-4">{title}</h3>
            <p className="text-gray-300">{description}</p>
          </div>
        </div>
      </Tilt3D>
    </motion.div>
  );
};

export default Security;
