import React, { useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import ModernHeader from '@/components/sections/ModernHeader';
import ModernFooter from '@/components/sections/ModernFooter';

const Cookies = () => {
  const heroRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  // Parallax effect for hero section
  const y = useTransform(scrollYProgress, [0, 1], [0, 200]);
  const opacity = useTransform(scrollYProgress, [0, 0.8], [1, 0]);

  return (
    <div className="min-h-screen w-full text-white overflow-x-hidden" style={{ background: '#0F0F0F' }}>
      <ModernHeader />

      {/* Hero Section */}
      <section ref={heroRef} className="py-16 relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #0F0F0F 0%, rgba(144, 25, 28, 0.8) 100%)' }}>
        {/* Background elements */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>
        </div>

        <motion.div
          style={{ y, opacity }}
          className="container mx-auto px-4 pt-20 pb-16 relative z-10"
        >
          <div className="text-center max-w-3xl mx-auto">
            <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-1.5 px-4 mx-auto block w-fit">
              Legal
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Cookie Policy
            </h1>
            <p className="text-xl text-gray-300">
              Last updated: {new Date().toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}
            </p>
          </div>
        </motion.div>
      </section>

      {/* Cookie Policy Content */}
      <section className="py-20 relative">
        <div className="container mx-auto px-4">
          <div className="glass-card rounded-xl p-8 border border-white/10 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-aha-red/5 to-transparent opacity-30"></div>
            <div className="relative z-10 prose prose-invert max-w-none">
              <h2>1. What Are Cookies</h2>
              <p>
                Cookies are small text files that are placed on your computer or mobile device when you visit a website. They are widely used to make websites work more efficiently and provide information to the website owners.
              </p>

              <h2>2. How We Use Cookies</h2>
              <p>
                AHA-Innovations uses cookies for various purposes, including:
              </p>
              <ul>
                <li><strong>Essential Cookies:</strong> These cookies are necessary for the website to function properly and cannot be switched off in our systems.</li>
                <li><strong>Performance Cookies:</strong> These cookies allow us to count visits and traffic sources so we can measure and improve the performance of our site.</li>
                <li><strong>Functional Cookies:</strong> These cookies enable the website to provide enhanced functionality and personalization.</li>
                <li><strong>Targeting Cookies:</strong> These cookies may be set through our site by our advertising partners to build a profile of your interests.</li>
              </ul>

              <h2>3. Types of Cookies We Use</h2>
              <p>
                We use the following types of cookies on our website:
              </p>
              <ul>
                <li><strong>Session Cookies:</strong> These are temporary cookies that are erased when you close your browser.</li>
                <li><strong>Persistent Cookies:</strong> These remain on your device until they expire or you delete them.</li>
                <li><strong>First-Party Cookies:</strong> These are cookies that are set by our website.</li>
                <li><strong>Third-Party Cookies:</strong> These are cookies that are set by third parties, such as analytics providers and advertising networks.</li>
              </ul>

              <h2>4. Specific Cookies We Use</h2>
              <p>
                Here is a list of the main cookies we use and what we use them for:
              </p>
              <ul>
                <li><strong>Authentication cookies:</strong> To identify you when you log in to our site.</li>
                <li><strong>Security cookies:</strong> To support security features and detect malicious activity.</li>
                <li><strong>Preference cookies:</strong> To remember information about how you prefer the site to behave and look.</li>
                <li><strong>Analytics cookies:</strong> To help us understand how visitors interact with our website.</li>
                <li><strong>Advertising cookies:</strong> To make advertising messages more relevant to you.</li>
              </ul>

              <h2>5. Managing Cookies</h2>
              <p>
                Most web browsers allow you to manage your cookie preferences. You can set your browser to refuse cookies, or to alert you when cookies are being sent. The methods for doing so vary from browser to browser, and from version to version.
              </p>
              <p>
                You can also use tools like the Digital Advertising Alliance's WebChoices tool to opt out of some third-party targeting cookies.
              </p>

              <h2>6. Changes to Our Cookie Policy</h2>
              <p>
                We may update our Cookie Policy from time to time. We will notify you of any changes by posting the new Cookie Policy on this page and updating the "Last updated" date.
              </p>

              <h2>7. Contact Us</h2>
              <p>
                If you have any questions about our Cookie Policy, please contact <NAME_EMAIL>.
              </p>
            </div>
          </div>
        </div>
      </section>

      <ModernFooter />
    </div>
  );
};

export default Cookies;
