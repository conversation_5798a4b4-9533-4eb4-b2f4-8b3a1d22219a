import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Chrome } from 'lucide-react'; // Or the correct Google icon name
import { GoogleAuthProvider, signInWithPopup, createUserWithEmailAndPassword } from 'firebase/auth';
import { auth } from '../firebase/config'; // Add this import
import Logo from '../components/Logo'; // Assuming you have a Logo component
import SocialButton from '../components/SocialButton'; // Assuming you have a SocialButton component for Google

interface SignUpFormData {
  email: string;
  password: string;
}

const SignUpFirebase: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<SignUpFormData>({ email: '', password: '' });
  const [error, setError] = useState<string | null>(null);

  const handleEmailPasswordSignUp = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null); // Clear previous errors

    try {
      const userCredential = await createUserWithEmailAndPassword(auth, formData.email, formData.password);
      const user = userCredential.user;
      console.log('Email/Password Sign-up successful:', user);
      // TODO: Implement backend call to create GHL user with email/password info
      // navigate('/dashboard'); // Example navigation after successful sign-up
    } catch (error: any) {
      console.error('Email/Password Sign-up error:', error);
      setError(error.message); // Set error state
    }
  };

  const handleGoogleSignIn = async () => {
    const provider = new GoogleAuthProvider();
    try {
      const result = await signInWithPopup(auth, provider);
      // The signed-in user info.
      const user = result.user;
      console.log('Google Sign-in successful:', user);
      // TODO: Implement backend call to check/create GHL user
      // navigate('/dashboard'); // Example navigation after successful sign-in
    } catch (error: any) {
      // Handle Errors here.
      // The AuthCredential type that was used.
      const credential = GoogleAuthProvider.credentialFromError(error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left Section */}
      <div className="w-full md:w-1/2 bg-gradient-to-r from-gray-900 to-gray-800 text-white flex flex-col justify-center items-center p-6 md:p-10">
        <div className="text-center">
          <div className="flex justify-center mb-6"><Logo /></div>
          <h1 className="text-3xl md:text-5xl font-bold mb-4 mt-4 md:mt-0">
            All-in-one platform for <span className="text-red-500">automating your</span> Business
          </h1>
          <p className="text-base md:text-xl text-gray-300">
            Streamline workflows, automate tasks, and get things done efficiently, so you can focus on growth
          </p>
        </div>
      </div>

      {/* Right Section - Form */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-6 md:p-10 bg-gray-800 bg-opacity-75">
        <div className="w-full max-w-md bg-gray-900 p-6 md:p-8 rounded-lg shadow-xl text-white">
          <h2 className="text-xl md:text-2xl font-bold mb-4 md:mb-6 text-center">Create an Account</h2>
          <p className="mb-4 md:mb-6 text-center text-gray-400 text-sm md:text-base">Join AHA-Innovations and automate your business</p>

          {error && <p className="text-red-500 text-center mb-4">{error}</p>}

          <form onSubmit={handleEmailPasswordSignUp}>
            <div className="mb-3 md:mb-4">
              <label className="block text-gray-300 text-xs md:text-sm font-bold mb-1 md:mb-2" htmlFor="email">
                Email *
              </label>
              <input
                className="shadow appearance-none border rounded w-full py-2 px-3 bg-white text-gray-900 leading-tight focus:outline-none focus:shadow-outline"
                id="email"
                type="email"
                name="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={handleInputChange}
                required
              />
            </div>
            <div className="mb-4 md:mb-6">
              <label className="block text-gray-300 text-xs md:text-sm font-bold mb-1 md:mb-2" htmlFor="password">
                Password *
              </label>
              <input
                className="shadow appearance-none border rounded w-full py-2 px-3 bg-white text-gray-900 mb-2 md:mb-3 leading-tight focus:outline-none focus:shadow-outline"
                id="password"
                type="password"
                name="password"
                placeholder="******************"
                value={formData.password}
                onChange={handleInputChange}
                required
              />
            </div>
            <div className="flex items-center justify-between">
              <button
                className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full"
                type="submit"
              >
                Sign Up
              </button>
            </div>
          </form>

          <div className="mt-4 md:mt-6 text-center">
            <p className="text-gray-400 mb-2 md:mb-4 text-xs md:text-base">OR</p>
            <button
              onClick={handleGoogleSignIn}
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full flex items-center justify-center gap-2"
            >
              <Chrome className="w-5 h-5" /> Sign in with Google
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUpFirebase;
