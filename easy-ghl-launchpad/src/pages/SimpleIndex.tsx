import React from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Logo from '@/components/Logo';

const SimpleIndex = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-aha-dark text-white">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-aha-dark/80 backdrop-blur-md border-b border-white/10">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Logo className="h-8 w-auto" />
            </div>
            <nav className="hidden md:flex items-center space-x-6">
              <Link to="/features" className="text-white/80 hover:text-white transition-colors">Features</Link>
              <Link to="/pricing" className="text-white/80 hover:text-white transition-colors">Pricing</Link>
              <Link to="/contact" className="text-white/80 hover:text-white transition-colors">Contact</Link>
            </nav>
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                className="text-white/80 hover:text-white hover:bg-white/10"
                onClick={() => navigate('/signin')}
              >
                Sign In
              </Button>
              <Button
                className="bg-aha-red hover:bg-aha-darkred text-white"
                onClick={() => navigate('/signup')}
              >
                Sign Up
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main>
        {/* Spacer for fixed header */}
        <div className="h-16"></div>

        {/* Hero Section */}
        <section className="auth-gradient relative">
          {/* Background elements */}
          <div className="absolute inset-0 z-0">
            <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
            <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>
          </div>

          {/* Content */}
          <div className="container mx-auto px-4 pt-20 pb-32 relative z-10">
            <div className="flex flex-col items-center text-center mb-12">
              <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-1.5 px-4">
                Welcome to AHA-Innovations
              </Badge>
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6">
                <span className="text-white">Streamline Your</span>
                <br />
                <span className="text-aha-red">Business Operations</span>
              </h1>
              <p className="text-xl text-gray-300 max-w-3xl">
                AHA-Innovations provides an all-in-one platform for small businesses and agencies to manage clients, 
                automate workflows, and grow their business.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mt-8">
                <Button
                  size="lg"
                  className="bg-aha-red hover:bg-aha-darkred text-white text-lg px-8 py-7"
                  onClick={() => navigate('/signup')}
                >
                  Try AHA Free
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white/20 text-white hover:bg-white/10 text-lg px-8 py-7"
                  onClick={() => navigate('/features')}
                >
                  Explore Features
                </Button>
              </div>
            </div>

            {/* Dashboard Preview Image */}
            <div className="relative w-full max-w-5xl mx-auto mt-12">
              <div className="relative">
                <img
                  src="/lovable-uploads/c32a6868-d00e-4a6e-b758-8ac0467695f9.png"
                  alt="AHA-Innovations Dashboard"
                  className="w-full rounded-lg shadow-2xl border border-white/10"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="bg-aha-dark py-24">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-5xl font-bold mb-4">
                <span className="text-white">Powerful Features for Your</span>
                <span className="text-aha-red"> Business</span>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Everything you need to manage and grow your business in one place
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Feature Card 1 */}
              <div className="bg-white/5 rounded-xl p-6 border border-white/10 hover:border-aha-red/30 transition-all duration-300">
                <div className="w-12 h-12 bg-aha-red/20 rounded-lg flex items-center justify-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-aha-red">
                    <path d="M5 12h14"></path>
                    <path d="M12 5v14"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-2">Client Management</h3>
                <p className="text-gray-400">
                  Easily manage all your clients in one place with detailed profiles and communication history.
                </p>
              </div>

              {/* Feature Card 2 */}
              <div className="bg-white/5 rounded-xl p-6 border border-white/10 hover:border-aha-red/30 transition-all duration-300">
                <div className="w-12 h-12 bg-aha-red/20 rounded-lg flex items-center justify-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-aha-red">
                    <path d="M12 2v4"></path>
                    <path d="M12 18v4"></path>
                    <path d="M4.93 4.93l2.83 2.83"></path>
                    <path d="M16.24 16.24l2.83 2.83"></path>
                    <path d="M2 12h4"></path>
                    <path d="M18 12h4"></path>
                    <path d="M4.93 19.07l2.83-2.83"></path>
                    <path d="M16.24 7.76l2.83-2.83"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-2">Workflow Automation</h3>
                <p className="text-gray-400">
                  Automate repetitive tasks and workflows to save time and reduce errors.
                </p>
              </div>

              {/* Feature Card 3 */}
              <div className="bg-white/5 rounded-xl p-6 border border-white/10 hover:border-aha-red/30 transition-all duration-300">
                <div className="w-12 h-12 bg-aha-red/20 rounded-lg flex items-center justify-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-aha-red">
                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                    <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                    <line x1="12" y1="22.08" x2="12" y2="12"></line>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-2">Analytics & Reporting</h3>
                <p className="text-gray-400">
                  Get insights into your business performance with detailed analytics and reports.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="premium-gradient py-20">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              <span className="text-white">Ready to </span>
              <span className="text-aha-red">Grow Your Business?</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              Join thousands of businesses that use AHA-Innovations to streamline their operations and grow.
            </p>
            <Button
              size="lg"
              className="bg-aha-red hover:bg-aha-darkred text-white text-lg px-8 py-7"
              onClick={() => navigate('/signup')}
            >
              Get Started Today
            </Button>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-aha-dark border-t border-white/10 py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <Logo className="h-8 w-auto mb-4" />
              <p className="text-gray-400 mb-4">
                Empowering businesses with innovative solutions.
              </p>
            </div>
            <div>
              <h3 className="text-white font-bold mb-4">Product</h3>
              <ul className="space-y-2">
                <li><Link to="/features" className="text-gray-400 hover:text-white transition-colors">Features</Link></li>
                <li><Link to="/pricing" className="text-gray-400 hover:text-white transition-colors">Pricing</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-white font-bold mb-4">Company</h3>
              <ul className="space-y-2">
                <li><Link to="/about" className="text-gray-400 hover:text-white transition-colors">About Us</Link></li>
                <li><Link to="/contact" className="text-gray-400 hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-white font-bold mb-4">Legal</h3>
              <ul className="space-y-2">
                <li><Link to="/privacy" className="text-gray-400 hover:text-white transition-colors">Privacy Policy</Link></li>
                <li><Link to="/terms" className="text-gray-400 hover:text-white transition-colors">Terms of Service</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-white/10 mt-8 pt-8 text-center text-gray-400">
            <p>© {new Date().getFullYear()} AHA-Innovations. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default SimpleIndex;
