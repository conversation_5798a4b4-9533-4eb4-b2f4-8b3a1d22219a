import { useLocation } from "react-router-dom";
import { useEffect } from "react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-aha-dark text-white">
      <div className="text-center px-4">
        <h1 className="text-6xl font-bold mb-6 text-aha-red">404</h1>
        <p className="text-2xl mb-8">Oops! This page doesn't exist</p>
        <a 
          href="/" 
          className="bg-aha-red hover:bg-aha-darkred text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
        >
          Return to Home
        </a>
      </div>
    </div>
  );
};

export default NotFound;
