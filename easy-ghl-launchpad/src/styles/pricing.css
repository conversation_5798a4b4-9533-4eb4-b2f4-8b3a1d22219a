/* Pricing page specific styles */

.pricing-content {
  /* Ensure content remains visible */
  opacity: 1 !important;
}

/* Enhance glass card effect - more translucent, less blurry */
.glass-card {
  background: rgba(20, 20, 30, 0.15);
  backdrop-filter: blur(7px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: rgba(20, 20, 30, 0.2);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Add subtle glow effect */
.glow-effect {
  position: relative;
  overflow: hidden;
}

.glow-effect::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.5s ease;
}

.glow-effect:hover::before {
  opacity: 1;
}

/* Best seller banner styling */
.best-seller-banner {
  background: linear-gradient(90deg, rgba(255, 215, 0, 0.8), rgba(255, 165, 0, 0.8));
  color: #000;
  font-weight: bold;
  text-align: center;
  padding: 0.5rem;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 20;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -100% 0; }
  100% { background-position: 200% 0; }
}

/* Pricing card hover effect */
.pricing-card {
  transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275),
              box-shadow 0.4s ease;
  position: relative;
  overflow: hidden;
}

.pricing-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

/* Add floating animation to pricing cards */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
  100% { transform: translateY(0px); }
}

.float-animation {
  animation: float 4s ease-in-out infinite;
}

/* Ensure table content is visible with improved styling */
.pricing-table {
  background-color: rgba(20, 20, 30, 0.2);
  backdrop-filter: blur(7px);
  border-radius: 12px;
  overflow: hidden;
}

.pricing-table th {
  background-color: rgba(20, 20, 30, 0.3);
  font-weight: 600;
}

.pricing-table tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Ensure text remains visible against any background */
.pricing-content .text-white {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Enhance button hover effects */
.pricing-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.pricing-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.pricing-button:hover::after {
  transform: translateX(100%);
}

/* Toggle switch styling */
.toggle-switch {
  background: rgba(20, 20, 30, 0.3);
  border-radius: 30px;
  padding: 3px;
  display: inline-flex;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.toggle-switch button {
  border-radius: 30px;
  padding: 8px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.toggle-switch button.active {
  color: #fff;
}

.toggle-switch .slider {
  position: absolute;
  height: calc(100% - 6px);
  top: 3px;
  border-radius: 30px;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 0;
}
