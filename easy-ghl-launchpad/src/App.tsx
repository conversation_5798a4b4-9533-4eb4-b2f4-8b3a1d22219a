
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { ThemeProvider } from "next-themes";
// import Index from "./pages/Index";
import SimpleIndex from "./pages/SimpleIndex";
// Using relative import path
import ModernIndex from "./pages/ModernIndex";
import SignIn from "./pages/SignIn";
import SignUp from "./pages/SignUp";
import EmbeddedSignUp from "./pages/EmbeddedSignUp";
import SimpleSignUp from "./pages/SimpleSignUp";
import TestPortfolioForm from "./pages/TestPortfolioForm";
import ForgotPassword from "./pages/ForgotPassword";
import Dashboard from "./pages/Dashboard";
import NotFound from "./pages/NotFound";
import GHLAuthCallback from "./components/GHLAuthCallback";
import GHLInitiate from "./pages/GHLInitiate";
import Pricing from "./pages/Pricing";
import Contact from "./pages/Contact";
import Features from "./pages/Features";
import Showcase from "./pages/Showcase";
import SetupGHLToken from "./pages/setup-ghl-token";
import About from "./pages/About";
import Terms from "./pages/Terms";
import Privacy from "./pages/Privacy";
import Cookies from "./pages/Cookies";
import GDPR from "./pages/GDPR";
import SignUpFirebase from "./pages/SignUpFirebase";
import Security from "./pages/Security";

// Import CSS
import "./App.css";

const queryClient = new QueryClient();

// Main application URL
const MAIN_APP_URL = '/';

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider attribute="class" defaultTheme="dark">
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<ModernIndex />} />
            <Route path="/classic" element={<SimpleIndex />} />
            <Route path="/pricing" element={<Pricing />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/features" element={<Features />} />
            <Route path="/showcase" element={<Showcase />} />
            <Route path="/signin" element={<SignIn />} />
            <Route path="/signup" element={<SignUp />} />
            <Route path="/signupfire" element={<SignUpFirebase />} />
            <Route path="/embedded-signup" element={<EmbeddedSignUp />} />
            <Route path="/test-form" element={<TestPortfolioForm />} />
            <Route path="/simple-signup" element={<SimpleSignUp />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            {/* The Dashboard page and OAuth callback handler */}
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/auth/callback" element={<Navigate to="/dashboard" replace />} />
            {/* GHL OAuth routes */}
            <Route path="/ghl-auth-callback" element={<GHLAuthCallback />} />
            <Route path="/initiate" element={<GHLInitiate />} />
            <Route path="/setup-ghl-token" element={<SetupGHLToken />} />
            {/* Legal and Company Pages */}
            <Route path="/about" element={<About />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/cookies" element={<Cookies />} />
            <Route path="/gdpr" element={<GDPR />} />
            <Route path="/security" element={<Security />} />
            {/* Handle 404 errors */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
