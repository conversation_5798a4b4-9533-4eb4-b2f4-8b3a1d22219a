import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';

interface Tilt3DProps {
  children: React.ReactNode;
  className?: string;
  intensity?: number;
  perspective?: number;
  reset?: boolean;
}

const Tilt3D: React.FC<Tilt3DProps> = ({
  children,
  className = '',
  intensity = 10,
  perspective = 1000,
  reset = true,
}) => {
  const [rotateX, setRotateX] = useState(0);
  const [rotateY, setRotateY] = useState(0);
  const elementRef = useRef<HTMLDivElement>(null);

  // Use a more direct DOM manipulation approach to avoid animation conflicts
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    element.style.transform = `perspective(${perspective}px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
  }, [rotateX, rotateY, perspective]);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const width = rect.width;
    const height = rect.height;
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    const rY = ((mouseX / width) - 0.5) * intensity;
    const rX = ((0.5 - (mouseY / height))) * intensity;

    setRotateX(rX);
    setRotateY(rY);
  };

  const handleMouseLeave = () => {
    if (reset) {
      setRotateX(0);
      setRotateY(0);
    }
  };

  return (
    <div
      ref={elementRef}
      className={`${className} transition-transform duration-200`}
      style={{
        transformStyle: 'preserve-3d',
      }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
    >
      {children}
    </div>
  );
};

export default Tilt3D;
