import React from 'react';
import { motion } from 'framer-motion';
import { useWizard } from '@/components/ui/step-wizard';
import { AnimatedInput } from '@/components/ui/animated-input';
import { Label } from '@/components/ui/label';
import { Building2, User, MapPin, Check } from 'lucide-react';

// Step 1: Company Information
export const CompanyStep = () => {
  const { formData, updateFormData } = useWizard();

  return (
    <motion.div
      className="space-y-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="flex justify-center mb-4"
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5, type: "spring" }}
      >
        <div className="w-16 h-16 rounded-full bg-gradient-to-br from-aha-red/30 to-aha-red/10 flex items-center justify-center backdrop-blur-sm border border-aha-red/20">
          <Building2 size={32} className="text-aha-red" />
        </div>
      </motion.div>

      <motion.h3
        className="text-xl font-bold text-center mb-2"
        initial={{ y: -10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        Tell us about your business
      </motion.h3>

      <motion.p
        className="text-gray-400 text-center mb-6"
        initial={{ y: -5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        We'll use this information to set up your account
      </motion.p>

      <motion.div
        className="space-y-4"
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <AnimatedInput
          id="companyName"
          label="Company/Agency Name"
          value={formData.companyName || ''}
          onChange={(e) => updateFormData({ companyName: e.target.value })}
          placeholder="Your company name"
          required
        />
      </motion.div>
    </motion.div>
  );
};

// Step 2: Personal Information
export const PersonalStep = () => {
  const { formData, updateFormData } = useWizard();

  return (
    <motion.div
      className="space-y-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="flex justify-center mb-4"
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5, type: "spring" }}
      >
        <div className="w-16 h-16 rounded-full bg-gradient-to-br from-aha-red/30 to-aha-red/10 flex items-center justify-center backdrop-blur-sm border border-aha-red/20">
          <User size={32} className="text-aha-red" />
        </div>
      </motion.div>

      <motion.h3
        className="text-xl font-bold text-center mb-2"
        initial={{ y: -10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        Your personal details
      </motion.h3>

      <motion.p
        className="text-gray-400 text-center mb-6"
        initial={{ y: -5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        Tell us a bit about yourself
      </motion.p>

      <motion.div
        className="space-y-4"
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <div className="grid grid-cols-2 gap-4">
          <AnimatedInput
            id="firstName"
            label="First Name"
            value={formData.firstName || ''}
            onChange={(e) => updateFormData({ firstName: e.target.value })}
            placeholder="First name"
            required
          />
          <AnimatedInput
            id="lastName"
            label="Last Name"
            value={formData.lastName || ''}
            onChange={(e) => updateFormData({ lastName: e.target.value })}
            placeholder="Last name"
            required
          />
        </div>

        <AnimatedInput
          id="phone"
          label="Phone Number (Optional)"
          value={formData.phone || ''}
          onChange={(e) => updateFormData({ phone: e.target.value })}
          placeholder="Your phone number"
          type="tel"
        />
      </motion.div>
    </motion.div>
  );
};

// Step 3: Location Information
export const LocationStep = () => {
  const { formData, updateFormData } = useWizard();

  return (
    <motion.div
      className="space-y-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="flex justify-center mb-4"
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5, type: "spring" }}
      >
        <div className="w-16 h-16 rounded-full bg-gradient-to-br from-aha-red/30 to-aha-red/10 flex items-center justify-center backdrop-blur-sm border border-aha-red/20">
          <MapPin size={32} className="text-aha-red" />
        </div>
      </motion.div>

      <motion.h3
        className="text-xl font-bold text-center mb-2"
        initial={{ y: -10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        Where are you located?
      </motion.h3>

      <motion.p
        className="text-gray-400 text-center mb-6"
        initial={{ y: -5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        This information is optional but helps us serve you better
      </motion.p>

      <motion.div
        className="space-y-4"
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <div className="grid grid-cols-2 gap-4">
          <AnimatedInput
            id="city"
            label="City (Optional)"
            value={formData.city || ''}
            onChange={(e) => updateFormData({ city: e.target.value })}
            placeholder="Your city"
          />
          <AnimatedInput
            id="state"
            label="State/Province (Optional)"
            value={formData.state || ''}
            onChange={(e) => updateFormData({ state: e.target.value })}
            placeholder="Your state"
          />
        </div>

        <AnimatedInput
          id="country"
          label="Country"
          value={formData.country || ''}
          onChange={(e) => {
            // Log the country value for debugging
            console.log('Country input changed to:', e.target.value);
            updateFormData({ country: e.target.value });
          }}
          placeholder="Your country (e.g., Philippines, United States)"
        />
        <motion.p
          className="text-xs text-gray-500"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          Enter your full country name (e.g., Philippines, United States)
        </motion.p>
      </motion.div>
    </motion.div>
  );
};

// Step 4: Review & Confirm
export const ReviewStep = () => {
  const { formData, wizardContext } = useWizard();

  // Log the context and user email for debugging
  console.log('ReviewStep: wizardContext:', wizardContext);
  console.log('ReviewStep: user email:', wizardContext?.user?.email);

  return (
    <motion.div
      className="space-y-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="flex justify-center mb-4"
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5, type: "spring" }}
      >
        <div className="w-16 h-16 rounded-full bg-gradient-to-br from-green-500/30 to-green-500/10 flex items-center justify-center backdrop-blur-sm border border-green-500/20">
          <Check size={32} className="text-green-500" />
        </div>
      </motion.div>

      <motion.h3
        className="text-xl font-bold text-center mb-2"
        initial={{ y: -10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        Almost there!
      </motion.h3>

      <motion.p
        className="text-gray-400 text-center mb-6"
        initial={{ y: -5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        Review your information and complete setup
      </motion.p>

      <motion.div
        className="space-y-4 bg-gray-800/30 backdrop-blur-sm p-6 rounded-lg border border-gray-700/50"
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        whileHover={{ boxShadow: "0 0 20px rgba(234, 56, 76, 0.1)" }}
      >
        <div className="grid grid-cols-2 gap-3">
          <motion.div
            className="text-gray-400"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            Company:
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: -5 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            {formData.companyName}
          </motion.div>

          <motion.div
            className="text-gray-400"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.45 }}
          >
            Name:
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: -5 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.45 }}
          >
            {formData.firstName} {formData.lastName}
          </motion.div>

          {/* Always show email from user context with proper null checking */}
          <motion.div
            className="text-gray-400"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            Email:
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: -5 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
          >
            {(() => {
              // Try to get email from different possible locations
              const email = wizardContext?.user?.email;

              // Log the email for debugging
              console.log('ReviewStep: Email from context:', email);

              if (email) {
                return email;
              } else {
                return 'Not available - Please sign in again';
              }
            })()}
          </motion.div>

          {formData.phone && (
            <>
              <motion.div
                className="text-gray-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.55 }}
              >
                Phone:
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: -5 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.55 }}
              >
                {formData.phone}
              </motion.div>
            </>
          )}

          {(formData.city || formData.state || formData.country) && (
            <>
              <motion.div
                className="text-gray-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
              >
                Location:
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: -5 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
              >
                {[formData.city, formData.state, formData.country]
                  .filter(Boolean)
                  .join(', ')}
              </motion.div>
            </>
          )}

          {formData.role && (
            <>
              <motion.div
                className="text-gray-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.65 }}
              >
                Role:
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: -5 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.65 }}
              >
                {formData.role}
              </motion.div>
            </>
          )}

          {formData.company_size && (
            <>
              <motion.div
                className="text-gray-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.7 }}
              >
                Company Size:
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: -5 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.7 }}
              >
                {formData.company_size}
              </motion.div>
            </>
          )}

          {formData.referral_source && (
            <>
              <motion.div
                className="text-gray-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.75 }}
              >
                Heard About Us From:
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: -5 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.75 }}
              >
                {formData.referral_source}
              </motion.div>
            </>
          )}
        </div>
      </motion.div>

      <motion.p
        className="text-sm text-gray-400 text-center mt-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.8 }}
      >
        By completing setup, you agree to our Terms of Service and Privacy Policy
      </motion.p>
    </motion.div>
  );
};
