import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { callEdgeFunction } from '@/utils/supabaseClient';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const EdgeFunctionTester = () => {
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testCheckUserExists = async () => {
    if (!email) {
      setError('Please enter an email address');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const data = await callEdgeFunction('check-ghl-user-exists', { email });
      setResult(data);
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      setResult(null);
    } finally {
      setLoading(false);
    }
  };

  const testGetCredentials = async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await callEdgeFunction('get-ghl-credentials');
      // Mask sensitive data for display
      const maskedData = {
        clientId: data.clientId ? '✓ Present' : '✗ Missing',
        clientSecret: data.clientSecret ? '✓ Present' : '✗ Missing',
        agencyToken: data.agencyToken ? '✓ Present' : '✗ Missing',
        companyId: data.companyId ? '✓ Present' : '✗ Missing',
      };
      setResult(maskedData);
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      setResult(null);
    } finally {
      setLoading(false);
    }
  };

  const testTriggerN8nWorkflow = async () => {
    if (!email || !firstName || !lastName) {
      setError('Please enter email, first name, and last name');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const data = await callEdgeFunction('trigger-n8n-workflow', {
        email,
        firstName,
        lastName,
        companyName: companyName || `${firstName}'s Business`,
        password: 'TestPassword123!',
        source: 'edge_function_tester',
        timestamp: new Date().toISOString()
      });
      setResult(data);
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      setResult(null);
    } finally {
      setLoading(false);
    }
  };

  const testN8nWorkflow = async () => {
    if (!email || !firstName || !lastName) {
      setError('Please enter email, first name, and last name');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const data = await callEdgeFunction('test-n8n-workflow', {
        email,
        firstName,
        lastName,
        companyName: companyName || `${firstName}'s Business`,
        password: 'TestPassword123!',
        source: 'edge_function_tester',
        timestamp: new Date().toISOString()
      });
      setResult(data);
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      setResult(null);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Edge Function Tester</CardTitle>
        <CardDescription>Test Supabase Edge Functions</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              <p>{error}</p>
            </div>
          )}

          {result && (
            <div className="bg-gray-100 p-4 rounded overflow-auto max-h-60">
              <pre className="text-sm">{JSON.stringify(result, null, 2)}</pre>
            </div>
          )}
        </div>
      </CardContent>
      <CardContent className="pt-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              placeholder="John"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              className="mb-2"
            />
          </div>
          <div>
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              placeholder="Doe"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              className="mb-2"
            />
          </div>
        </div>
        <div>
          <Label htmlFor="companyName">Company Name</Label>
          <Input
            id="companyName"
            placeholder="Acme Inc."
            value={companyName}
            onChange={(e) => setCompanyName(e.target.value)}
            className="mb-2"
          />
        </div>
      </CardContent>

      <CardFooter className="flex flex-wrap gap-2 justify-between">
        <Button
          variant="outline"
          onClick={testGetCredentials}
          disabled={loading}
          size="sm"
        >
          {loading ? 'Loading...' : 'Test Get Credentials'}
        </Button>
        <Button
          onClick={testCheckUserExists}
          disabled={loading}
          size="sm"
        >
          {loading ? 'Loading...' : 'Test Check User'}
        </Button>
        <Button
          onClick={testTriggerN8nWorkflow}
          disabled={loading}
          variant="default"
          className="bg-green-600 hover:bg-green-700 text-white"
          size="sm"
        >
          {loading ? 'Loading...' : 'Test n8n Webhook'}
        </Button>
        <Button
          onClick={testN8nWorkflow}
          disabled={loading}
          variant="default"
          className="bg-blue-600 hover:bg-blue-700 text-white"
          size="sm"
        >
          {loading ? 'Loading...' : 'Test n8n Function'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default EdgeFunctionTester;
