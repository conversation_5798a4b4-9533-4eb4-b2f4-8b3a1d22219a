
import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { supabase, callEdgeFunction } from '@/utils/supabaseClient';

// Main application URL to redirect to after successful authentication
// Use localhost for local development, otherwise use production URL
const isLocalDevelopment = window.location.hostname === 'localhost';
const MAIN_APP_URL = isLocalDevelopment ? 'http://localhost:8080/dashboard' : 'https://app.aha-innovations.com/';

const GHLAuthCallback = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const location = useLocation();
  const { toast } = useToast();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        console.log("GHL Auth Callback received. Location:", location);

        // Parse the URL search params
        const searchParams = new URLSearchParams(location.search);
        const code = searchParams.get('code');
        const locationId = searchParams.get('locationId');
        const state = searchParams.get('state');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        console.log("GHL Auth params:", { code, locationId, state, error });

        // If there's an error in the callback
        if (error) {
          throw new Error(errorDescription || 'Authorization failed');
        }

        // Validate state parameter for security (if it was used)
        const savedState = localStorage.getItem('ghl_auth_state');
        if (savedState && state && savedState !== state) {
          throw new Error('Invalid state parameter');
        }

        // Clear state from localStorage
        localStorage.removeItem('ghl_auth_state');

        if (!code) {
          throw new Error('Authorization code not found in the callback URL');
        }

        // Check if user is authenticated
        const { data: { session } } = await supabase.auth.getSession();

        if (!session) {
          console.log("No session found, saving GHL data for later and redirecting to main app");

          // Store the GHL code and location in localStorage to retrieve after login at app.aha-innovations.com
          if (code) localStorage.setItem('ghl_pending_code', code);
          if (locationId) localStorage.setItem('ghl_pending_location_id', locationId);

          // Always redirect to main app, even without a session
          window.location.href = MAIN_APP_URL;
          return;
        }

        console.log("Session found, updating profile with GHL data");

        // Exchange the code for an access token
        const redirectUri = isLocalDevelopment
          ? 'http://localhost:8080/ghl-auth-callback'
          : 'https://app.aha-innovations.com/ghl-auth-callback';

        // Call the Edge Function using our secure utility
        const tokenData = await callEdgeFunction('exchange-ghl-auth-code', {
          code: code,
          redirectUri: redirectUri
        });

        // Update the user's profile with GHL data
        const { error: updateError } = await supabase
          .from('profiles')
          .upsert({
            id: session.user.id,
            ghl_auth_code: code,
            ghl_location_id: locationId,
            ghl_access_token: tokenData?.access_token,
            ghl_refresh_token: tokenData?.refresh_token,
            updated_at: new Date().toISOString(),
          });

        if (updateError) {
          console.error("Error updating profile:", updateError);
          throw updateError;
        }

        console.log("Profile updated successfully");

        // Add the successful connection information to localStorage so the main app knows
        // about the successful connection
        localStorage.setItem('ghl_connection_successful', 'true');

        // Immediately redirect to main application without showing toast on this app
        window.location.href = MAIN_APP_URL;

      } catch (err: any) {
        console.error('Error handling GHL auth callback:', err);
        setError(err.message);

        // Even on error, redirect to main app after a short delay
        setTimeout(() => {
          window.location.href = MAIN_APP_URL;
        }, 3000);
      } finally {
        setLoading(false);
      }
    };

    // Run the callback handler immediately
    handleCallback();
  }, [location, toast]);

  if (loading) {
    return (
      <div className="min-h-screen bg-aha-dark flex items-center justify-center">
        <p className="text-white">Connecting your GoHighLevel account... Redirecting automatically.</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-aha-dark flex items-center justify-center flex-col">
        <p className="text-white mb-4">Error connecting your GoHighLevel account:</p>
        <p className="text-red-400">{error}</p>
        <p className="text-white mt-4">Redirecting to {isLocalDevelopment ? 'dashboard' : 'app.aha-innovations.com'}...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-aha-dark flex items-center justify-center">
      <p className="text-white">GoHighLevel connected successfully. Redirecting to {isLocalDevelopment ? 'dashboard' : 'app.aha-innovations.com'}...</p>
    </div>
  );
};

export default GHLAuthCallback;
