import React from 'react';

interface CompetitorLogoProps {
  name: string;
}

// SVG logos for popular competitors
const CompetitorLogos: React.FC<CompetitorLogoProps> = ({ name }) => {
  // Map of competitor names to their brand colors
  const brandColors: Record<string, string> = {
    'HubSpot': '#FF7A59',
    'ClickFunnels': '#0073E6',
    'JotForm': '#0A1551',
    'Mailchimp': '#FFE01B',
    'Twilio': '#F22F46',
    'Wix': '#0C6EFC',
    'Calendly': '#006BFF',
    'Zapier': '#FF4A00',
    'Kajabi': '#00A2B8',
    'CallRail': '#F16334',
    'Yelp': '#D32323',
    'Google Analytics': '#E37400',
    'Slack': '#4A154B',
  };

  // Get the brand color or default to gray
  const bgColor = brandColors[name] || '#6B7280';

  // Determine text color based on background brightness
  const isLightBackground = ['#FFE01B'].includes(bgColor);
  const textColor = isLightBackground ? 'text-gray-900' : 'text-white';

  // SVG logos for each competitor
  const logoSvg = {
    'HubSpot': (
      <svg viewBox="0 0 512 512" className="w-5 h-5 text-white">
        <path fill="currentColor" d="M267.1 385.2c-22.9 0-41.5-18.6-41.5-41.5V267.1h82.9v76.6c.1 22.9-18.5 41.5-41.4 41.5zm41.4-158.9h-82.9v-76.6c0-22.9 18.6-41.5 41.5-41.5 22.9 0 41.5 18.6 41.5 41.5v76.6zm-166.7 76.6c0 22.9-18.6 41.5-41.5 41.5-22.9 0-41.5-18.6-41.5-41.5v-76.6h82.9v76.6zm-82.9-117.9h82.9v-76.6c0-22.9-18.6-41.5-41.5-41.5-22.9 0-41.5 18.6-41.5 41.5v76.6zm166.7-76.6v76.6h82.9v-76.6c0-22.9-18.6-41.5-41.5-41.5-22.9 0-41.4 18.6-41.4 41.5zm83 117.9h-83v76.6c0 22.9 18.6 41.5 41.5 41.5 22.9 0 41.5-18.6 41.5-41.5v-76.6z"/>
      </svg>
    ),
    'ClickFunnels': (
      <svg viewBox="0 0 512 512" className="w-5 h-5 text-white">
        <path fill="currentColor" d="M256 128c-70.7 0-128 57.3-128 128s57.3 128 128 128 128-57.3 128-128-57.3-128-128-128zm0 224c-52.9 0-96-43.1-96-96s43.1-96 96-96 96 43.1 96 96-43.1 96-96 96zm0-160c-35.3 0-64 28.7-64 64s28.7 64 64 64 64-28.7 64-64-28.7-64-64-64z"/>
      </svg>
    ),
    'Mailchimp': (
      <svg viewBox="0 0 512 512" className="w-5 h-5 text-black">
        <path fill="currentColor" d="M256 128c-70.7 0-128 57.3-128 128 0 47.3 25.7 88.7 64 111.1V256c0-35.3 28.7-64 64-64s64 28.7 64 64v111.1c38.3-22.4 64-63.8 64-111.1 0-70.7-57.3-128-128-128zm0 224c-52.9 0-96-43.1-96-96s43.1-96 96-96 96 43.1 96 96-43.1 96-96 96z"/>
      </svg>
    ),
    'Zapier': (
      <svg viewBox="0 0 512 512" className="w-5 h-5 text-white">
        <path fill="currentColor" d="M128 128h256v256H128V128zm224 224V160H160v192h192z"/>
        <path fill="currentColor" d="M176 176h160v160H176V176zm128 128V208H208v96h96z"/>
      </svg>
    ),
  };

  // Return the appropriate logo based on the competitor name
  if (logoSvg[name]) {
    return (
      <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{ backgroundColor: bgColor }}>
        {logoSvg[name]}
      </div>
    );
  }

  // For competitors without SVG logos, use the first letter
  return (
    <div
      className={`w-8 h-8 rounded-full flex items-center justify-center ${textColor}`}
      style={{ backgroundColor: bgColor }}
    >
      <span className="font-bold text-xs">{name.substring(0, 2).toUpperCase()}</span>
    </div>
  );
};

export default CompetitorLogos;
