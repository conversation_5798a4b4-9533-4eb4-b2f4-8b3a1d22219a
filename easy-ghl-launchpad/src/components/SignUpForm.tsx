
import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Github, Mail, Eye, EyeOff, Check } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import SocialButton from './SocialButton';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/utils/supabaseClient';
import { createGHLAccountOnSignUp, updateSupabaseProfileWithGHL } from '@/utils/ghlPrivateAuth';
import { callEdgeFunction } from '@/utils/supabaseClient';

const SignUpForm = () => {
  const [loading, setLoading] = useState(true);
  return (
    <div className="w-full h-[600px] flex items-center justify-center">
      <div className="relative w-full h-full">
        {/* Loader */}
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center z-10">
            <div className="relative flex items-center justify-center">
              {/* Spinning ring */}
              <span
                className="absolute inline-block rounded-full border-4"
                style={{
                  borderColor: '#E11D48', // Tailwind's rose-600 (main red)
                  borderTopColor: 'transparent',
                  width: '80px',
                  height: '80px',
                  animation: 'spin 1s linear infinite'
                }}
              ></span>
              {/* Pumping logo */}
              <img
                src="/Logomark Red.svg" // Update this path to your actual logo file
                alt="Loading..."
                className="h-12 w-12"
                style={{
                  animation: 'pump 1s infinite'
                }}
              />
              <style>
                {`
                  @keyframes pump {
                    0% { transform: scale(1);}
                    50% { transform: scale(1.15);}
                    100% { transform: scale(1);}
                  }
                  @keyframes spin {
                    100% { transform: rotate(360deg);}
                  }
                `}
              </style>
            </div>
          </div>
        )}
        {/* Iframe */}
        <iframe
          src="https://calendar.aha-innovations.com/widget/form/YN86bt2SlADr1BfnDEkq"
          width="100%"
          height="100%"
          id="inline-YN86bt2SlADr1BfnDEkq"
          data-layout="{'id':'INLINE'}"
          data-trigger-type="alwaysShow"
          data-trigger-value=""
          data-activation-type="alwaysActivated"
          data-activation-value=""
          data-deactivation-type="neverDeactivate"
          data-deactivation-value=""
          data-form-name="Sign Up form AHA embedded to site"
          data-height="689"
          data-layout-iframe-id="inline-YN86bt2SlADr1BfnDEkq"
          data-form-id="YN86bt2SlADr1BfnDEkq"
          title="Sign Up form AHA embedded to site"
          onLoad={() => setLoading(false)}
        />
        <script src="https://calendar.aha-innovations.com/js/form_embed.js"></script>
      </div>
    

    </div>
  );
  

};

export default SignUpForm;
