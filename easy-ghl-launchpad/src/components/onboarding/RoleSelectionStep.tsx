import React from 'react';
import { motion } from 'framer-motion';
import { Label } from '@/components/ui/label';
import { useWizard } from '@/components/ui/step-wizard';
import { cn } from '@/lib/utils';
import { OnboardingButton } from '@/components/ui/onboarding-button';

// Role options with their labels and identifiers
const roleOptions = [
  { id: 'hr_legal', label: 'HR & Legal', key: 'A' },
  { id: 'engineering', label: 'Engineering', key: 'B' },
  { id: 'it_support', label: 'IT & Support', key: 'C' },
  { id: 'marketing', label: 'Marketing', key: 'D' },
  { id: 'sales', label: 'Sales', key: 'E' },
  { id: 'product_design', label: 'Product & Design', key: 'F' },
  { id: 'customer_service', label: 'Customer Service', key: 'G' },
  { id: 'other', label: 'Other', key: 'H' },
];

const RoleSelectionStep = () => {
  const { formData, updateFormData } = useWizard();

  const handleRoleSelect = (roleId: string) => {
    updateFormData({ role: roleId });
  };

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="text-center space-y-2"
        initial={{ y: -10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <h2 className="text-2xl font-bold">What team are you on?*</h2>
        <p className="text-gray-400">
          This helps us to customize your experience in AHA-Innovations
        </p>
      </motion.div>

      <motion.div
        className="space-y-2 mt-6"
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        {roleOptions.map((role, index) => (
          <motion.button
            key={role.id}
            type="button"
            className={cn(
              "w-full flex items-center px-4 py-3 rounded-md border backdrop-blur-sm transition-all duration-300",
              formData.role === role.id
                ? "bg-gray-800/80 border-aha-red shadow-[0_0_10px_rgba(234,56,76,0.1)]"
                : "bg-gray-900/40 border-gray-700 hover:bg-gray-800/50 hover:border-gray-600"
            )}
            onClick={() => handleRoleSelect(role.id)}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 + (index * 0.05) }}
            whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
            whileTap={{ scale: 0.98 }}
            style={{ minHeight: '48px' }}
          >
            <motion.div
              className={cn(
                "flex items-center justify-center w-6 h-6 rounded-md mr-3 text-sm font-medium",
                formData.role === role.id ? "bg-aha-red text-white" : "bg-gray-800 text-gray-300"
              )}
              animate={{
                scale: formData.role === role.id ? [1, 1.1, 1] : 1,
              }}
              transition={{ duration: 0.3 }}
            >
              {role.key}
            </motion.div>
            <span>{role.label}</span>
            {formData.role === role.id && (
              <motion.div
                className="ml-auto text-aha-red"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring" }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M20 6L9 17l-5-5"></path>
                </svg>
              </motion.div>
            )}
          </motion.button>
        ))}
      </motion.div>
    </motion.div>
  );
};

export default RoleSelectionStep;
