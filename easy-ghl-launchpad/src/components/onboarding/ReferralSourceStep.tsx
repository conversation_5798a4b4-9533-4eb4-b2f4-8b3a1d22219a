import React from 'react';
import { motion } from 'framer-motion';
import { useWizard } from '@/components/ui/step-wizard';
import { cn } from '@/lib/utils';
import { OnboardingButton } from '@/components/ui/onboarding-button';

// Referral source options with their labels and identifiers
const referralSourceOptions = [
  { id: 'google', label: 'Google', key: 'A' },
  { id: 'twitter', label: 'Twitter', key: 'B' },
  { id: 'linkedin', label: 'LinkedIn', key: 'C' },
  { id: 'youtube', label: 'YouTube', key: 'D' },
  { id: 'word_of_mouth', label: 'Friend / Word of mouth', key: 'E' },
  { id: 'podcast', label: 'Podcast', key: 'F' },
  { id: 'event', label: 'Event', key: 'G' },
  { id: 'other', label: 'Other', key: 'H' },
];

const ReferralSourceStep = () => {
  const { formData, updateFormData } = useWizard();

  const handleReferralSourceSelect = (sourceId: string) => {
    updateFormData({ referral_source: sourceId });
  };

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="text-center space-y-2"
        initial={{ y: -10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <h2 className="text-2xl font-bold">How did you hear about AHA-Innovations?*</h2>
        <p className="text-gray-400">
          This helps us understand where our users come from
        </p>
      </motion.div>

      <motion.div
        className="space-y-2 mt-6"
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        {referralSourceOptions.map((source, index) => (
          <motion.button
            key={source.id}
            type="button"
            className={cn(
              "w-full flex items-center px-4 py-3 rounded-md border backdrop-blur-sm transition-all duration-300",
              formData.referral_source === source.id
                ? "bg-gray-800/80 border-aha-red shadow-[0_0_10px_rgba(234,56,76,0.1)]"
                : "bg-gray-900/40 border-gray-700 hover:bg-gray-800/50 hover:border-gray-600"
            )}
            onClick={() => handleReferralSourceSelect(source.id)}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 + (index * 0.05) }}
            whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
            whileTap={{ scale: 0.98 }}
            style={{ minHeight: '48px' }}
          >
            <motion.div
              className={cn(
                "flex items-center justify-center w-6 h-6 rounded-md mr-3 text-sm font-medium",
                formData.referral_source === source.id ? "bg-aha-red text-white" : "bg-gray-800 text-gray-300"
              )}
              animate={{
                scale: formData.referral_source === source.id ? [1, 1.1, 1] : 1,
              }}
              transition={{ duration: 0.3 }}
            >
              {source.key}
            </motion.div>
            <span>{source.label}</span>
            {formData.referral_source === source.id && (
              <motion.div
                className="ml-auto text-aha-red"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring" }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M20 6L9 17l-5-5"></path>
                </svg>
              </motion.div>
            )}
          </motion.button>
        ))}
      </motion.div>
    </motion.div>
  );
};

export default ReferralSourceStep;
