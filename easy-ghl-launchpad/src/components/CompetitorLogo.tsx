import React from 'react';

interface CompetitorLogoProps {
  name: string;
}

const CompetitorLogo: React.FC<CompetitorLogoProps> = ({ name }) => {
  // Map of competitor names to their brand colors
  const brandColors: Record<string, string> = {
    'HubSpot': '#FF7A59',
    'ClickFunnels': '#0073E6',
    'JotForm': '#0A1551',
    'Mailchimp': '#FFE01B',
    'Twilio': '#F22F46',
    'Wix': '#0C6EFC',
    'Calendly': '#006BFF',
    'Zapier': '#FF4A00',
    'Kajabi': '#00A2B8',
    'CallRail': '#F16334',
    'Yelp': '#D32323',
    'Google Analytics': '#E37400',
    'Slack': '#4A154B',
  };

  // Get the brand color or default to gray
  const bgColor = brandColors[name] || '#6B7280';
  
  // Get the first letter of the competitor name
  const firstLetter = name.charAt(0);
  
  // Determine text color based on background brightness
  const isLightBackground = ['#FFE01B'].includes(bgColor);
  const textColor = isLightBackground ? 'text-gray-900' : 'text-white';

  return (
    <div 
      className={`w-8 h-8 rounded-full flex items-center justify-center ${textColor}`}
      style={{ backgroundColor: bgColor }}
    >
      <span className="text-sm font-bold">{firstLetter}</span>
    </div>
  );
};

export default CompetitorLogo;
