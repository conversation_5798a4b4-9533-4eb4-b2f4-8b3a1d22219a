import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { createGHLSubaccountAndUser } from '@/utils/ghlClient';
import { useToast } from '@/hooks/use-toast';
import { createClient } from '@supabase/supabase-js';

// Import Supabase client from the centralized client
import { supabase } from '@/utils/supabaseClient';

interface OnboardingFormProps {
  user: any;
  onSuccess: (locationId: string) => void;
  onCancel: () => void;
}

const OnboardingForm = ({ user, onSuccess, onCancel }: OnboardingFormProps) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  // Form state
  const [companyName, setCompanyName] = useState(user?.user_metadata?.full_name ? `${user.user_metadata.full_name}'s Agency` : '');
  const [firstName, setFirstName] = useState(user?.user_metadata?.first_name || user?.user_metadata?.name?.split(' ')[0] || '');
  const [lastName, setLastName] = useState(user?.user_metadata?.last_name || user?.user_metadata?.name?.split(' ').slice(1).join(' ') || '');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [country, setCountry] = useState('United States');
  const [phone, setPhone] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!companyName || !firstName || !lastName) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);

      toast({
        title: "Setting Up Account",
        description: "Setting up your account. This may take a moment...",
      });

      // Create the GHL account with all the collected information
      const result = await createGHLSubaccountAndUser({
        email: user.email,
        firstName,
        lastName,
        companyName,
        // Additional information for better account setup
        city,
        state,
        country,
        phone,
      });

      if (result?.locationId) {
        // Update user profile with the new GHL location ID
        await supabase
          .from('profiles')
          .upsert({
            id: user.id,
            ghl_account_id: result.locationId,
            ghl_location_id: result.locationId,
            updated_at: new Date().toISOString(),
            // Store additional profile information
            company_name: companyName,
            first_name: firstName,
            last_name: lastName,
            city,
            state,
            country,
            phone,
          });

        toast({
          title: "Success!",
          description: result.existing
            ? "Your account has been set up successfully!"
            : "Your account has been set up successfully!",
        });

        // Call the success callback with the location ID
        onSuccess(result.locationId);
      } else {
        throw new Error("Failed to set up your account. Please try again.");
      }
    } catch (error: any) {
      console.error('Error creating GHL account:', error);

      toast({
        title: "Error",
        description: `Failed to set up your account: ${error.message || 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto bg-aha-darkpurple/80 border-gray-800 text-white">
      <CardHeader>
        <CardTitle>Complete Your Account Setup</CardTitle>
        <CardDescription className="text-gray-400">
          Please provide some information to complete your account setup
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="companyName">Company/Agency Name *</Label>
            <Input
              id="companyName"
              value={companyName}
              onChange={(e) => setCompanyName(e.target.value)}
              className="bg-aha-dark border-gray-700"
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name *</Label>
              <Input
                id="firstName"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                className="bg-aha-dark border-gray-700"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name *</Label>
              <Input
                id="lastName"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                className="bg-aha-dark border-gray-700"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city">City</Label>
              <Input
                id="city"
                value={city}
                onChange={(e) => setCity(e.target.value)}
                className="bg-aha-dark border-gray-700"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="state">State/Province</Label>
              <Input
                id="state"
                value={state}
                onChange={(e) => setState(e.target.value)}
                className="bg-aha-dark border-gray-700"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="country">Country</Label>
            <Input
              id="country"
              value={country}
              onChange={(e) => setCountry(e.target.value)}
              className="bg-aha-dark border-gray-700"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              className="bg-aha-dark border-gray-700"
              type="tel"
            />
          </div>

          <div className="pt-2">
            <p className="text-xs text-gray-400 mb-2">* Required fields</p>
          </div>

          <div className="flex justify-between pt-2">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="border-gray-700 text-gray-300 hover:bg-gray-800"
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-green-600 hover:bg-green-700 text-white"
              disabled={loading}
            >
              {loading ? "Setting Up Account..." : "Complete Setup"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default OnboardingForm;
