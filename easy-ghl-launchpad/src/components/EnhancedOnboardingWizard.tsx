import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { StepWizard, Step } from '@/components/ui/step-wizard';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { OnboardingButton } from '@/components/ui/onboarding-button';
import { GlassCard } from '@/components/ui/glass-card';
import { supabase } from '@/utils/supabaseClient';
import { createGHLAccountOnSignUp, updateSupabaseProfileWithGHL } from '@/utils/ghlPrivateAuth';
import { fetchUserProfileForOnboarding, OnboardingFormData } from '@/utils/autofillOnboarding';
import axios from 'axios';

// Import the original steps
import { CompanyStep, PersonalStep, LocationStep, ReviewStep } from './OnboardingSteps';

// Import our new steps
import { RoleSelectionStep, CompanySizeStep, ReferralSourceStep } from './onboarding';

interface EnhancedOnboardingWizardProps {
  user: any;
  onSuccess: (locationId: string) => void;
  onCancel: () => void;
}

const EnhancedOnboardingWizard: React.FC<EnhancedOnboardingWizardProps> = ({ user, onSuccess, onCancel }) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [initialData, setInitialData] = useState<OnboardingFormData | null>(null);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [isTyping, setIsTyping] = useState(false);

  // Effect to detect typing state from body class
  useEffect(() => {
    const checkTypingState = () => {
      setIsTyping(document.body.classList.contains('is-typing'));
    };

    // Set up a mutation observer to watch for class changes on body
    const observer = new MutationObserver(checkTypingState);
    observer.observe(document.body, { attributes: true, attributeFilter: ['class'] });

    // Initial check
    checkTypingState();

    // Cleanup
    return () => observer.disconnect();
  }, []);

  // Log user object for debugging
  console.log('EnhancedOnboardingWizard: User object:', user);
  console.log('EnhancedOnboardingWizard: User email:', user?.email);

  // Create a safe user object to prevent null reference errors
  const safeUser = user || {};

  // Ensure we have the user's email
  let userEmail = safeUser.email;

  if (!userEmail) {
    console.warn('EnhancedOnboardingWizard: User email is missing');
    // Try to get email from user metadata
    if (safeUser.user_metadata && safeUser.user_metadata.email) {
      userEmail = safeUser.user_metadata.email;
      console.log('EnhancedOnboardingWizard: Retrieved email from user_metadata:', userEmail);
    } else if (safeUser.identities && safeUser.identities[0] && safeUser.identities[0].identity_data && safeUser.identities[0].identity_data.email) {
      userEmail = safeUser.identities[0].identity_data.email;
      console.log('EnhancedOnboardingWizard: Retrieved email from identities:', userEmail);
    }
  }

  // Create a local copy of the user object with the email
  const enhancedUser = {
    ...safeUser,
    email: userEmail
  };

  // Get user's name from metadata with better fallbacks
  const userName = safeUser?.user_metadata?.full_name ||
                  safeUser?.user_metadata?.name ||
                  (safeUser?.user_metadata?.first_name ?
                    (safeUser?.user_metadata?.last_name ?
                      `${safeUser.user_metadata.first_name} ${safeUser.user_metadata.last_name}` :
                      safeUser.user_metadata.first_name) :
                    (userEmail?.split('@')[0] || 'friend'));

  // Get data from localStorage if available (from social sign-up)
  const storedFullName = localStorage.getItem('aha_signup_fullname') || '';
  const storedCompanyName = localStorage.getItem('aha_signup_company') || '';

  // Split stored full name into first and last name if available
  let storedFirstName = '';
  let storedLastName = '';
  if (storedFullName) {
    const nameParts = storedFullName.trim().split(' ');
    storedFirstName = nameParts[0] || '';
    storedLastName = nameParts.slice(1).join(' ') || '';
  }

  // Fetch user profile data for autofilling the form
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoadingData(true);

        // Fetch user profile data from the edge function
        const profileData = await fetchUserProfileForOnboarding(user?.id, user?.email);

        // Combine with localStorage data if available
        const combinedData = {
          ...profileData,
          // Override with localStorage data if available
          firstName: storedFirstName || profileData.firstName,
          lastName: storedLastName || profileData.lastName,
          companyName: storedCompanyName || profileData.companyName,
        };

        console.log('EnhancedOnboardingWizard: Fetched user profile data:', combinedData);

        // Set the initial form data
        setInitialData(combinedData);
      } catch (error) {
        console.error('Error fetching user profile data:', error);

        // Fallback to basic data if fetch fails
        const fallbackData: OnboardingFormData = {
          companyName: storedCompanyName ||
                      user?.profile_data?.company_name ||
                      user?.user_metadata?.company_name ||
                      (user?.user_metadata?.full_name ? `${user.user_metadata.full_name}'s Agency` : ''),
          firstName: storedFirstName ||
                    user?.profile_data?.first_name ||
                    user?.user_metadata?.first_name ||
                    user?.user_metadata?.name?.split(' ')[0] || '',
          lastName: storedLastName ||
                    user?.profile_data?.last_name ||
                    user?.user_metadata?.last_name ||
                    user?.user_metadata?.name?.split(' ').slice(1).join(' ') || '',
          // Pre-fill optional fields if available
          phone: user?.profile_data?.phone || '',
          city: user?.profile_data?.city || '',
          state: user?.profile_data?.state || '',
          country: user?.profile_data?.country || 'United States',
          // Initialize new fields
          role: user?.profile_data?.role || user?.user_metadata?.role || '',
          company_size: user?.profile_data?.company_size || user?.user_metadata?.company_size || '',
          referral_source: user?.profile_data?.referral_source || user?.user_metadata?.referral_source || '',
        };

        setInitialData(fallbackData);
      } finally {
        setIsLoadingData(false);
      }
    };

    fetchUserData();
  }, [user?.id, user?.email]);

  // Log all available data sources for debugging
  console.log('EnhancedOnboardingWizard: Data sources:', {
    storedData: {
      fullName: storedFullName,
      firstName: storedFirstName,
      lastName: storedLastName,
      companyName: storedCompanyName
    },
    profileData: user?.profile_data,
    userMetadata: user?.user_metadata,
    initialFormData: initialData
  });

  const handleComplete = async (formData: any) => {
    // Validate required fields
    if (!formData.companyName || !formData.firstName || !formData.lastName) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);

      toast({
        title: "Setting Up Account",
        description: "Setting up your account. This may take a moment...",
      });

      // PRIORITY: Use the private integration (edge function) first
      console.log('Using private integration via edge function...');

      try {
        // Validate that we have an email
        if (!userEmail) {
          throw new Error("User email is required but not available. Please try signing in again.");
        }

        // Log the form data for debugging
        console.log('Form data being sent to GHL:', {
          email: userEmail,
          firstName: formData.firstName,
          lastName: formData.lastName,
          companyName: formData.companyName,
          city: formData.city,
          state: formData.state,
          country: formData.country,
          phone: formData.phone,
          role: formData.role,
          company_size: formData.company_size,
          referral_source: formData.referral_source
        });

        // Create the GHL account with all the collected information using private integration
        const result = await createGHLAccountOnSignUp({
          email: userEmail,
          firstName: formData.firstName,
          lastName: formData.lastName,
          companyName: formData.companyName,
          // Additional information
          city: formData.city || '',
          state: formData.state || '',
          country: formData.country || '',
          phone: formData.phone || '',
          // New onboarding information
          role: formData.role || '',
          company_size: formData.company_size || '',
          referral_source: formData.referral_source || '',
        });

        if (result.success && result.locationId) {
          // Update user profile with the new GHL location ID and additional information
          await supabase
            .from('profiles')
            .upsert({
              id: user.id,
              ghl_account_id: result.locationId,
              ghl_location_id: result.locationId,
              updated_at: new Date().toISOString(),
              // Store additional profile information
              company_name: formData.companyName,
              first_name: formData.firstName,
              last_name: formData.lastName,
              city: formData.city || '',
              state: formData.state || '',
              country: formData.country || '',
              phone: formData.phone || '',
              // Store new onboarding information
              role: formData.role || '',
              company_size: formData.company_size || '',
              referral_source: formData.referral_source || '',
            });

          // Also update user metadata
          await supabase.auth.updateUser({
            data: {
              role: formData.role || '',
              company_size: formData.company_size || '',
              referral_source: formData.referral_source || '',
            }
          });

          toast({
            title: "Success!",
            description: "Your account has been set up successfully!",
          });

          // Call the success callback with the location ID
          onSuccess(result.locationId);
          return;
        } else {
          throw new Error("Failed to set up your account with private integration. Trying fallback method...");
        }
      } catch (privateIntegrationError) {
        console.error('Error using private integration:', privateIntegrationError);
        // If the private integration fails, we'll fall back to the n8n webhook
        console.log('Falling back to n8n webhook...');

        // FALLBACK: Try the n8n webhook if private integration fails
        try {
          // Format country for n8n webhook
          let formattedCountry = formData.country || '';

          // If country is provided but not in the correct format, try to convert it
          if (formattedCountry && formattedCountry.length > 2) {
            // For n8n webhook, we need to use 2-letter country code
            // For simplicity, we'll just use US for United States and PH for Philippines
            if (formattedCountry.toLowerCase().includes('philippines')) {
              formattedCountry = 'PH';
            } else {
              // Default to US for other countries
              formattedCountry = 'US';
            }
            console.log('Converted country to code for n8n webhook:', formattedCountry);
          } else if (!formattedCountry) {
            // Default to US if no country is provided
            formattedCountry = 'US';
          }

          // Prepare the request body for n8n - match the GHL API structure
          const n8nRequestBody = {
            // Main location data
            name: formData.companyName || `${formData.firstName}'s Agency`,
            phone: formData.phone || '',
            companyId: "gPkTndcx94O3r573TOMx", // Agency ID
            address: formData.address || '123 Main Street',
            city: formData.city || 'Default City',
            state: formData.state || 'Default State',
            country: formattedCountry, // Use formatted country code
            postalCode: formData.postalCode || '12345',
            website: formData.website || '',
            timezone: 'US/Central',

            // Prospect info (required)
            prospectInfo: {
              firstName: formData.firstName,
              lastName: formData.lastName,
              email: userEmail
            },

            // Settings
            settings: {
              allowDuplicateContact: false,
              allowDuplicateOpportunity: false,
              allowFacebookNameMerge: false,
              disableContactTimezone: false
            },

            // Social media links
            social: {
              facebookUrl: '',
              googlePlus: '',
              linkedIn: '',
              foursquare: '',
              twitter: '',
              yelp: '',
              instagram: '',
              youtube: '',
              pinterest: '',
              blogRss: '',
              googlePlacesId: ''
            },

            // Additional custom fields for our app
            custom_data: {
              user_email: user.email,
              password: 'defaultPassword123!', // This would be replaced in production
              role: formData.role || '',
              company_size: formData.company_size || '',
              referral_source: formData.referral_source || '',
              source: 'onboarding_wizard_fallback',
              timestamp: new Date().toISOString()
            }
          };

          // Call the test webhook directly
          const testWebhookUrl = 'https://n8n-1-i8dz.onrender.com/webhook-test/aha-signup';
          console.log('Sending data to test webhook:', testWebhookUrl);
          console.log('Request body:', JSON.stringify(n8nRequestBody));

          const response = await axios.post(testWebhookUrl, n8nRequestBody);
          console.log('Test webhook response:', response.data);

          // Update user profile with the form data
          await supabase
            .from('profiles')
            .upsert({
              id: user.id,
              updated_at: new Date().toISOString(),
              // Store additional profile information
              company_name: formData.companyName,
              first_name: formData.firstName,
              last_name: formData.lastName,
              city: formData.city || '',
              state: formData.state || '',
              country: formData.country || '',
              phone: formData.phone || '',
              // Store new onboarding information
              role: formData.role || '',
              company_size: formData.company_size || '',
              referral_source: formData.referral_source || '',
            });

          // Also update user metadata
          await supabase.auth.updateUser({
            data: {
              role: formData.role || '',
              company_size: formData.company_size || '',
              referral_source: formData.referral_source || '',
            }
          });

          toast({
            title: "Success!",
            description: "Your account has been set up successfully using fallback method!",
          });

          // For now, we'll use a mock location ID
          const mockLocationId = "test-location-" + Math.floor(Math.random() * 1000);

          // Call the success callback with the mock location ID
          onSuccess(mockLocationId);
          return;
        } catch (webhookError) {
          console.error('Error calling fallback webhook:', webhookError);
          throw new Error("All account creation methods failed. Please try again later.");
        }
      }
    } catch (error: any) {
      console.error('Error setting up account:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to set up your account. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="relative min-h-screen w-full flex items-center justify-center py-10 px-4">
      {/* Animated background with gradient */}
      <motion.div
        className="fixed inset-0 -z-10 bg-gradient-to-br from-gray-900 via-gray-800 to-black"
        animate={{
          background: isTyping
            ? [
                "linear-gradient(135deg, #1a1a2e, #16213e, #0f3460)",
                "linear-gradient(135deg, #1e1a2e, #2e1a3e, #3e1a4e)",
                "linear-gradient(135deg, #1a1a2e, #16213e, #0f3460)"
              ]
            : [
                "linear-gradient(135deg, #1a1a2e, #16213e, #0f3460)",
                "linear-gradient(135deg, #16213e, #0f3460, #1a1a2e)",
                "linear-gradient(135deg, #0f3460, #1a1a2e, #16213e)"
              ]
        }}
        transition={{
          duration: isTyping ? 5 : 20,
          repeat: Infinity,
          repeatType: "reverse"
        }}
      />

      {/* Animated particles/stars effect */}
      <div className="fixed inset-0 -z-5 overflow-hidden opacity-30">
        {Array.from({ length: 20 }).map((_, i) => (
          <motion.div
            key={i}
            className={`absolute rounded-full ${isTyping ? "bg-aha-red/70" : "bg-white"}`}
            style={{
              width: Math.random() * 3 + 1,
              height: Math.random() * 3 + 1,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: isTyping ? [0.2, 0.7, 0.2] : [0.1, 0.5, 0.1],
              scale: isTyping ? [1, 2, 1] : [1, 1.5, 1],
            }}
            transition={{
              duration: isTyping ? Math.random() * 2 + 1 : Math.random() * 5 + 5,
              repeat: Infinity,
              repeatType: "reverse",
              delay: Math.random() * 5,
            }}
          />
        ))}
      </div>

      {/* Animated gradient line at top */}
      <motion.div
        className={`fixed top-0 left-0 right-0 ${isTyping ? 'h-2' : 'h-1'} bg-gradient-to-r from-aha-red via-blue-500 to-aha-red -z-5`}
        animate={{
          backgroundPosition: isTyping ? ["0% 0%", "100% 0%", "0% 0%"] : ["0% 0%", "100% 0%"],
          boxShadow: isTyping ? "0 0 10px rgba(234, 56, 76, 0.5)" : "none"
        }}
        transition={{
          backgroundPosition: {
            duration: isTyping ? 3 : 8,
            repeat: Infinity,
            repeatType: "reverse"
          },
          boxShadow: {
            duration: 0.5
          }
        }}
        style={{ backgroundSize: "200% 100%" }}
      />

      {/* Additional animated line at bottom when typing */}
      {isTyping && (
        <motion.div
          className="fixed bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-aha-red to-blue-500 -z-5"
          initial={{ scaleX: 0, opacity: 0 }}
          animate={{
            scaleX: 1,
            opacity: 1,
            backgroundPosition: ["0% 0%", "100% 0%"]
          }}
          exit={{ scaleX: 0, opacity: 0 }}
          transition={{
            scaleX: { duration: 0.5 },
            opacity: { duration: 0.5 },
            backgroundPosition: { duration: 3, repeat: Infinity, repeatType: "reverse" }
          }}
          style={{
            backgroundSize: "200% 100%",
            transformOrigin: "left",
            boxShadow: "0 0 10px rgba(59, 130, 246, 0.5)"
          }}
        />
      )}

      {/* Animated gradient orbs */}
      <div className="fixed inset-0 -z-5 overflow-hidden opacity-20">
        {Array.from({ length: 3 }).map((_, i) => (
          <motion.div
            key={`orb-${i}`}
            className={`absolute rounded-full blur-xl ${
              isTyping
                ? "bg-gradient-to-r from-aha-red/40 to-blue-500/40"
                : "bg-gradient-to-r from-aha-red/30 to-blue-500/30"
            }`}
            style={{
              width: Math.random() * 300 + 100,
              height: Math.random() * 300 + 100,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
            }}
            animate={{
              x: isTyping
                ? [Math.random() * 50, Math.random() * -50, Math.random() * 50]
                : [Math.random() * 100, Math.random() * -100, Math.random() * 100],
              y: isTyping
                ? [Math.random() * 50, Math.random() * -50, Math.random() * 50]
                : [Math.random() * 100, Math.random() * -100, Math.random() * 100],
              scale: isTyping ? [1, 1.3, 0.9, 1] : [1, 1.2, 0.8, 1],
              rotate: isTyping ? [0, 90, 0] : [0, 180, 0],
            }}
            transition={{
              duration: isTyping ? Math.random() * 20 + 10 : Math.random() * 60 + 30,
              repeat: Infinity,
              repeatType: "reverse",
            }}
          />
        ))}
      </div>

      <GlassCard
        className="w-full max-w-md mx-auto p-0 overflow-visible relative z-10 my-8"
        variant="dark"
        glow={true}
        hoverEffect={true}
        borderGlow={true}
        borderGlowColor="red"
      >
        <motion.div
          className="p-6 pb-0"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <motion.h2
            className="text-center text-3xl font-bold text-white"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            Hi {userName.split(' ')[0]}
          </motion.h2>
          <motion.p
            className="text-gray-300 text-center text-base mt-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Answer these questions to get the most out of your AHA-Innovations trial
          </motion.p>
        </motion.div>

        <div className="p-6">
          {isLoadingData ? (
            <div className="flex flex-col items-center justify-center py-8">
              <motion.div
                className="rounded-full h-12 w-12 border-4 border-aha-red/30 border-t-aha-red"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              />
              <motion.p
                className="mt-4 text-gray-300"
                initial={{ opacity: 0 }}
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                Loading your information...
              </motion.p>
            </div>
          ) : (
            <StepWizard
              onComplete={handleComplete}
              onCancel={onCancel}
              initialData={initialData || {}}
              context={{ user: enhancedUser }}
              showWelcomeAnimation={true}
            >
              {/* New onboarding steps */}
              <Step title="Role">
                <RoleSelectionStep />
              </Step>
              <Step title="Company Size">
                <CompanySizeStep />
              </Step>
              <Step title="Referral">
                <ReferralSourceStep />
              </Step>

              {/* Original onboarding steps */}
              <Step title="Business">
                <CompanyStep />
              </Step>
              <Step title="Personal">
                <PersonalStep />
              </Step>
              <Step title="Location" isOptional={true}>
                <LocationStep />
              </Step>
              <Step title="Review">
                <ReviewStep />
              </Step>
            </StepWizard>
          )}
        </div>
      </GlassCard>
    </div>
  );
};

export default EnhancedOnboardingWizard;
