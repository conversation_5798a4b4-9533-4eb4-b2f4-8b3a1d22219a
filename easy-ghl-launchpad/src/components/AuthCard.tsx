
import React from 'react';

interface AuthCardProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
}

const AuthCard: React.FC<AuthCardProps> = ({ children, title, subtitle }) => {
  return (
    <div className="w-full max-w-md p-8 bg-aha-dark/70 backdrop-blur-xl rounded-xl shadow-2xl border border-gray-800/50 relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute -top-20 -right-20 w-40 h-40 bg-aha-red/20 rounded-full blur-3xl"></div>
      <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-aha-red/20 rounded-full blur-3xl"></div>
      <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-10 z-0"></div>

      <div className="relative z-10">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-white">{title}</h2>
          {subtitle && <p className="text-gray-300 mt-1">{subtitle}</p>}
        </div>
        {children}
      </div>
    </div>
  );
};

export default AuthCard;
