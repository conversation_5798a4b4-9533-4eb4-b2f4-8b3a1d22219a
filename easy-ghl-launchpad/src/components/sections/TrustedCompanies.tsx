import React, { useRef } from 'react';
import { motion } from 'framer-motion';

// Company logo interface
interface CompanyLogo {
  name: string;
  imageSrc: string;
}

const TrustedCompanies: React.FC = () => {
  // List of trusted companies with their logo images
  const companies: CompanyLogo[] = [
    {
      name: 'Company 1',
      imageSrc: '/Company logos (1).png'
    },
    {
      name: 'Company 2',
      imageSrc: '/Company logos (2).png'
    },
    {
      name: 'Company 3',
      imageSrc: '/Company logos (3).png'
    },
    {
      name: 'Company 4',
      imageSrc: '/Company logos (4).png'
    }
  ];

  // Create a much larger array with duplicated logos to ensure continuous scrolling without gaps
  // We need enough logos to fill the screen width multiple times
  const extendedCompanies = [
    ...companies, ...companies, ...companies, ...companies, ...companies,
    ...companies, ...companies, ...companies, ...companies, ...companies
  ];

  // Create refs for the marquee containers
  const topRowRef = useRef<HTMLDivElement>(null);
  const bottomRowRef = useRef<HTMLDivElement>(null);

  // Stats to display
  const stats = [
    { value: "$250M+", label: "Processed" },
    { value: "50K+", label: "Hours saved" }
  ];

  return (
    <section className="py-8 md:py-16 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-64 md:w-96 h-64 md:h-96 bg-gradient-to-br from-aha-red/5 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[120%] h-[120%] bg-gradient-radial from-aha-red/10 via-transparent to-transparent opacity-40"></div>
      </div>

      {/* Stats section - Centered with container */}
      <div className="container mx-auto px-4 relative z-10 mb-8 md:mb-16">
        <div className="flex justify-center gap-16 md:gap-24 mb-12 md:mb-16">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 * index }}
              className="flex flex-col items-center"
            >
              <h3 className="text-3xl sm:text-4xl md:text-5xl font-bold font-gotham mb-1">{stat.value}</h3>
              <p className="text-gray-500 text-xs md:text-sm uppercase tracking-wider font-opensans">{stat.label}</p>
            </motion.div>
          ))}
        </div>

        <div className="flex items-center justify-center mb-8 md:mb-10">
          <div className="h-px w-16 bg-gradient-to-r from-transparent via-gray-700 to-transparent"></div>
          <motion.h3
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-center text-sm md:text-base text-gray-400 mx-4 font-opensans"
          >
            Trusted by hundreds of companies worldwide
          </motion.h3>
          <div className="h-px w-16 bg-gradient-to-r from-transparent via-gray-700 to-transparent"></div>
        </div>
      </div>

      {/* Company logos with horizontal scrolling animation */}
      {/* Top row - right to left animation */}
      <div className="relative w-full overflow-hidden mb-4 md:mb-8">
        {/* Cloudy edge effect - left side */}
        <div className="absolute left-0 top-0 bottom-0 w-16 md:w-40 z-20 bg-gradient-to-r from-black via-black/80 to-transparent pointer-events-none backdrop-blur-sm">
          <div className="absolute inset-0 bg-gradient-to-r from-aha-red/5 to-transparent opacity-50"></div>
        </div>

        <div className="relative w-full overflow-hidden">
          <div
            ref={topRowRef}
            className="flex animate-marquee-reverse"
            style={{
              width: "max-content",
              animationDuration: "40s",
              animationTimingFunction: "linear",
              animationIterationCount: "infinite"
            }}
          >
            {/* Extended set of logos for continuous scrolling */}
            {extendedCompanies.map((company, index) => (
              <div key={`top-${index}`} className="flex-shrink-0 px-1 md:px-2">
                <div
                  className="w-20 h-20 md:w-24 md:h-24 mx-auto rounded-lg flex items-center justify-center p-2 md:p-3 grayscale opacity-50 hover:opacity-80 hover:grayscale-0 transition-all duration-300"
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.02)',
                    backdropFilter: 'blur(2px)'
                  }}
                >
                  <img
                    src={company.imageSrc}
                    alt={company.name}
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Cloudy edge effect - right side */}
        <div className="absolute right-0 top-0 bottom-0 w-16 md:w-40 z-20 bg-gradient-to-l from-black via-black/80 to-transparent pointer-events-none backdrop-blur-sm">
          <div className="absolute inset-0 bg-gradient-to-l from-aha-red/5 to-transparent opacity-50"></div>
        </div>
      </div>

      {/* Bottom row - left to right animation */}
      <div className="relative w-full overflow-hidden">
        {/* Cloudy edge effect - left side */}
        <div className="absolute left-0 top-0 bottom-0 w-16 md:w-40 z-20 bg-gradient-to-r from-black via-black/80 to-transparent pointer-events-none backdrop-blur-sm">
          <div className="absolute inset-0 bg-gradient-to-r from-aha-red/5 to-transparent opacity-50"></div>
        </div>

        <div className="relative w-full overflow-hidden">
          <div
            ref={bottomRowRef}
            className="flex animate-marquee"
            style={{
              width: "max-content",
              animationDuration: "50s",
              animationTimingFunction: "linear",
              animationIterationCount: "infinite"
            }}
          >
            {/* Extended set of logos (reversed) for continuous scrolling */}
            {[...extendedCompanies].reverse().map((company, index) => (
              <div key={`bottom-${index}`} className="flex-shrink-0 px-1 md:px-2">
                <div
                  className="w-20 h-20 md:w-24 md:h-24 mx-auto rounded-lg flex items-center justify-center p-2 md:p-3 grayscale opacity-50 hover:opacity-80 hover:grayscale-0 transition-all duration-300"
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.02)',
                    backdropFilter: 'blur(2px)'
                  }}
                >
                  <img
                    src={company.imageSrc}
                    alt={company.name}
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Cloudy edge effect - right side */}
        <div className="absolute right-0 top-0 bottom-0 w-16 md:w-40 z-20 bg-gradient-to-l from-black via-black/80 to-transparent pointer-events-none backdrop-blur-sm">
          <div className="absolute inset-0 bg-gradient-to-l from-aha-red/5 to-transparent opacity-50"></div>
        </div>
      </div>
    </section>
  );
};

export default TrustedCompanies;
