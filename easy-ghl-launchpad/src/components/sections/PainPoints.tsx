import React from 'react';
import { motion } from 'framer-motion';
import { Badge } from '../ui/badge';
import {
  Layers,
  TrendingDown,
  Clock,
  Search,
  Code,
  Lightbulb
} from 'lucide-react';

interface PainPointProps {
  icon: React.ReactNode;
  title: string;
  delay?: number;
}

const PainPoint: React.FC<PainPointProps> = ({ icon, title, delay = 0 }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 + (delay * 0.1) }}
      viewport={{ once: true }}
      className="flex items-center gap-4 p-4 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10 hover:border-aha-red/20 transition-all duration-300 group"
    >
      <div className="w-12 h-12 rounded-full bg-aha-red/20 flex items-center justify-center text-white group-hover:bg-aha-red/30 transition-all duration-300">
        {icon}
      </div>
      <div>
        <h3 className="font-semibold text-white font-gotham">{title}</h3>
      </div>
    </motion.div>
  );
};

const PainPoints: React.FC = () => {
  const painPoints = [
    {
      icon: <Layers size={24} />,
      title: "Too many tools, too much chaos",
    },
    {
      icon: <TrendingDown size={24} />,
      title: "Low conversions, high costs",
    },
    {
      icon: <Clock size={24} />,
      title: "Manual tasks draining your time",
    },
    {
      icon: <Search size={24} />,
      title: "Hard to track leads & results",
    },
    {
      icon: <Code size={24} />,
      title: "Tech setup feels overwhelming",
    },
    {
      icon: <Lightbulb size={24} />,
      title: "Lack of ideas?",
    },
  ];

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
        <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="bg-aha-red/20 hover:bg-aha-red/30 text-aha-red border-none mb-6 py-1.5 px-4 mx-auto block w-fit font-medium">
            Pain Points
          </Badge>
          <h2 className="text-4xl md:text-5xl font-extrabold mb-6 tracking-tight font-gotham">
            Struggling with <span className="text-aha-red">these?</span>
          </h2>
          <p className="text-center text-gray-400 max-w-3xl mx-auto text-lg font-opensans">
            You're not alone. These are the common challenges our customers faced before finding AHA-Innovations.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {painPoints.map((point, index) => (
            <PainPoint
              key={index}
              icon={point.icon}
              title={point.title}
              delay={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default PainPoints;
