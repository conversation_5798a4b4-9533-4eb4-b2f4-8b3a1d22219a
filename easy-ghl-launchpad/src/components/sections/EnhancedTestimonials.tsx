import React from 'react';
import { motion } from 'framer-motion';
import { Badge } from '../ui/badge';

// Testimonial interface
interface Testimonial {
  name: string;
  title: string;
  company: string;
  avatarSrc: string;
  quote: string;
  logoSrc?: string;
}

const TestimonialCard: React.FC<{ testimonial: Testimonial; index: number }> = ({
  testimonial,
  index
}) => {
  return (
    <motion.div
      className="bg-[#1a1a3a] rounded-xl p-8 flex flex-col h-[400px] transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/20 border border-[#2a2a4a] hover:border-aha-red/30"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
      whileHover={{ y: -5 }}
    >
      {testimonial.logoSrc && (
        <div className="mb-4">
          <img src={testimonial.logoSrc} alt={`${testimonial.company} logo`} className="h-8" />
        </div>
      )}

      {/* Quote text */}
      <p className="text-gray-300 mb-8 text-base leading-relaxed flex-grow line-clamp-6 font-opensans">{testimonial.quote}</p>

      {/* Author info */}
      <div className="flex items-center mt-auto">
        <div className="h-12 w-12 mr-4 rounded-full overflow-hidden flex-shrink-0">
          <img src={testimonial.avatarSrc} alt={testimonial.name} className="h-full w-full object-cover" />
        </div>
        <div>
          <h4 className="font-bold text-white text-base font-gotham">{testimonial.name}</h4>
          <p className="text-sm text-gray-400 font-opensans">{testimonial.title}</p>
        </div>
      </div>
    </motion.div>
  );
};

const EnhancedTestimonials: React.FC = () => {
  // Featured testimonials with realistic data
  const testimonials: Testimonial[] = [
    {
      name: "Sarah Johnson",
      title: "VP of Marketing",
      company: "TechGrowth",
      avatarSrc: "https://randomuser.me/api/portraits/women/32.jpg",
      quote: "We hired AHA-Innovations to implement our marketing automation platform and build our sales team. They equipped our team with the automation needed to connect with our audience with ease. Our experience with them has been professional and responsive.",
      logoSrc: "/logos/techgrowth.svg"
    },
    {
      name: "Aaron Tench",
      title: "CEO",
      company: "VR Listing",
      avatarSrc: "https://randomuser.me/api/portraits/men/45.jpg",
      quote: "AHA-Innovations was great to work with. They helped our team become more aligned and accountable. Access to a dedicated Growth Coach was a game-changer for us. I highly recommend them to teams looking to create better alignment.",
      logoSrc: "/logos/vrlisting.svg"
    },
    {
      name: "Brett Montrose",
      title: "CEO",
      company: "Streamline Affiliate",
      avatarSrc: "https://randomuser.me/api/portraits/men/22.jpg",
      quote: "AHA-Innovations has been fundamental to helping us build and implement an effective sales process. Their knowledge and expertise gave us exactly what we needed as we enter our next phase of growth.",
      logoSrc: "/logos/streamline.svg"
    }
  ];

  return (
    <section className="py-24 bg-[#0F0F0F] relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-aha-red/5 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-tr from-aha-red/5 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[120%] h-[120%] bg-gradient-radial from-aha-red/10 via-transparent to-transparent opacity-40"></div>
      </div>

      <div className="relative z-10">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl md:text-6xl font-bold text-center mb-16 md:mb-20 font-gotham">
            <span className="text-white">See what </span>
            <span className="text-aha-red">our clients</span>
            <span className="text-white"> are saying</span>
          </h2>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard
                key={index}
                testimonial={testimonial}
                index={index}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Wave shape at bottom */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="w-full">
          <path fill="#ffffff" fillOpacity="0.05" d="M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,224C672,213,768,171,864,149.3C960,128,1056,128,1152,149.3C1248,171,1344,213,1392,234.7L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
        </svg>
      </div>
    </section>
  );
};

export default EnhancedTestimonials;
