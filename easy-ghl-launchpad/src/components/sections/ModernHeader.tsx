import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import Logo from '../Logo';
import { Button } from '../ui/button';
import { Menu, X } from 'lucide-react';

const ModernHeader: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location.pathname]);

  const navItems = [
    { label: 'Home', path: '/' },
    { label: 'Features', path: '/features' },
    { label: 'Pricing', path: '/pricing' },
    { label: 'Contact', path: '/contact' },
  ];

  return (
    <>
      <motion.header
        className={`w-full py-4 px-6 fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled ? 'glass-dark shadow-lg' : 'bg-transparent'
        }`}
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="container mx-auto flex justify-between items-center">
          <motion.div
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <Logo />
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-6">
            {navItems.map((item) => (
              <Button
                key={item.path}
                variant="ghost"
                className="text-white hover:bg-white/10 relative group"
                onClick={() => navigate(item.path)}
              >
                {item.label}
                <motion.div
                  className="absolute bottom-0 left-0 h-0.5 bg-aha-red"
                  initial={{ width: location.pathname === item.path ? "100%" : 0 }}
                  whileHover={{ width: "100%" }}
                  transition={{ duration: 0.3 }}
                />
              </Button>
            ))}
            <Button
              variant="ghost"
              className="text-white hover:bg-white/10 relative group"
              onClick={() => { window.location.href = 'https://app.aha-innovations.com'; }}
            >
              Sign In
              <motion.div
                className="absolute bottom-0 left-0 h-0.5 bg-aha-red"
                initial={{ width: 0 }}
                whileHover={{ width: "100%" }}
                transition={{ duration: 0.3 }}
              />
            </Button>
          </div>

          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="hidden md:block"
          >
            <Button
              className="bg-gradient-to-r from-aha-red to-aha-darkred hover:from-aha-darkred hover:to-aha-red text-white relative overflow-hidden group shadow-lg shadow-aha-red/20"
              onClick={() => navigate('/signup')}
            >
              <span className="relative z-10">Sign Up</span>
              <motion.div
                className="absolute inset-0 bg-white/20 z-0 backdrop-blur-sm"
                initial={{ x: '-100%' }}
                whileHover={{ x: '0%' }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}
              />
            </Button>
          </motion.div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden text-white"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </Button>
        </div>
      </motion.header>

      {/* Mobile Menu - Simplified for better performance */}
      <AnimatePresence mode="wait">
        {isMobileMenuOpen && (
          <motion.div
            className="fixed inset-0 bg-aha-dark/95 backdrop-blur-sm z-40 pt-20 px-6 md:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex flex-col gap-4">
              {navItems.map((item, index) => (
                <div key={item.path}>
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-white hover:bg-white/10 py-4"
                    onClick={() => {
                      setIsMobileMenuOpen(false);
                      setTimeout(() => navigate(item.path), 100);
                    }}
                  >
                    {item.label}
                  </Button>
                </div>
              ))}
              <div>
                <Button
                  variant="ghost"
                  className="w-full justify-start text-white hover:bg-white/10 py-4"
                  onClick={() => {
                    setIsMobileMenuOpen(false);
                    setTimeout(() => { window.location.href = 'https://app.aha-innovations.com'; }, 100);
                  }}
                >
                  Sign In
                </Button>
              </div>
              <div className="mt-4">
                <Button
                  className="w-full bg-aha-red hover:bg-aha-darkred text-white shadow-md relative"
                  onClick={() => {
                    setIsMobileMenuOpen(false);
                    setTimeout(() => navigate('/signup'), 100);
                  }}
                >
                  Sign Up
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Spacer for fixed header */}
      <div className="h-16"></div>
    </>
  );
};

export default ModernHeader;
