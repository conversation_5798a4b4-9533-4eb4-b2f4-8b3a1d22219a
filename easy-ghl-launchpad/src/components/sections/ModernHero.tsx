import React, { useRef, useEffect, useState } from 'react';
import { motion, useScroll, useTransform, AnimatePresence } from 'framer-motion';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { useNavigate } from 'react-router-dom';
import { ArrowRight, Code, Zap, Sparkles } from 'lucide-react';
import { useIsMobile } from '../../hooks/use-mobile';

interface ModernHeroProps {
  title: string;
  subtitle: string;
  description: string;
  ctaText: string;
  ctaLink: string;
}

const ModernHero: React.FC<ModernHeroProps> = ({
  title,
  subtitle,
  description,
  ctaText,
  ctaLink,
}) => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const containerRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const [hasScrolled, setHasScrolled] = useState(false);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  // Enhanced parallax effects
  const y = useTransform(scrollYProgress, [0, 1], [0, 200]);
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);
  const scale = useTransform(scrollYProgress, [0, 0.5], [1, 0.9]);

  // Typography scaling effects
  const titleScale = useTransform(scrollYProgress, [0, 0.2], [1, 1.15]);
  const titleY = useTransform(scrollYProgress, [0, 0.2], [0, -20]);

  // Background elements movement with enhanced effects
  const bgCircle1Y = useTransform(scrollYProgress, [0, 1], [0, -100]);
  const bgCircle2Y = useTransform(scrollYProgress, [0, 1], [0, -150]);
  const bgCircle3Y = useTransform(scrollYProgress, [0, 1], [0, -50]);

  // Background color accent intensity
  const bgAccentOpacity = useTransform(scrollYProgress, [0, 0.2], [0.05, 0.3]);

  // Additional transform values for elements
  const centerGradientOpacity = useTransform(scrollYProgress, [0, 0.2], [0.1, 0.4]);
  const rightGradientOpacity = useTransform(scrollYProgress, [0, 0.2], [0.4, 0.7]);
  const leftGradientOpacity = useTransform(scrollYProgress, [0, 0.2], [0.4, 0.7]);

  // Additional accent elements transforms
  const accentOpacity = useTransform(scrollYProgress, [0, 0.1, 0.2], [0, 0.3, 0.5]);
  const accentScale = useTransform(scrollYProgress, [0, 0.2], [0.8, 1.2]);

  // Typography and paragraph transforms
  const paragraphOpacity = useTransform(scrollYProgress, [0, 0.1], [1, 0.8]);
  const paragraphY = useTransform(scrollYProgress, [0, 0.1], [0, 20]);

  // Dashboard preview transforms
  const dashboardScale = useTransform(scrollYProgress, [0, 0.2], [1, hasScrolled ? 1.1 : 1]);
  const dashboardY = useTransform(scrollYProgress, [0, 0.2], [0, hasScrolled ? -20 : 0]);
  const dashboardGlowOpacity = useTransform(scrollYProgress, [0, 0.15], [0.6, 1]);
  const dashboardImageScale = useTransform(scrollYProgress, [0, 0.2], [1, hasScrolled ? 1.05 : 1]);

  // Feature card transforms
  const featureCardScale = useTransform(scrollYProgress, [0, 0.15], [1, 1.05]);
  const featureCard1Scale = useTransform(scrollYProgress, [0, 0.1], [1, hasScrolled ? 1.05 : 1]);
  const featureCard2Scale = useTransform(scrollYProgress, [0, 0.1], [1, hasScrolled ? 1.08 : 1]);
  const featureCard3Scale = useTransform(scrollYProgress, [0, 0.1], [1, hasScrolled ? 1.05 : 1]);

  // Additional glow effects
  const topGlowOpacity = useTransform(scrollYProgress, [0, 0.15], [0.3, 0.7]);
  const bottomGlowOpacity = useTransform(scrollYProgress, [0, 0.15], [0.2, 0.6]);
  const centerGlowOpacity = useTransform(scrollYProgress, [0, 0.15], [0, 0.5]);

  // Listen for scroll to trigger animations
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50 && !hasScrolled) {
        setHasScrolled(true);
      } else if (window.scrollY <= 50 && hasScrolled) {
        setHasScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasScrolled]);

  return (
    <section
      ref={containerRef}
      className="relative overflow-hidden min-h-[90vh] flex items-center bg-black"
    >
      {/* Enhanced background elements with dynamic color accents */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Main background gradients with scroll-based intensity */}
        <motion.div
          className="absolute top-1/4 left-1/4 w-64 md:w-96 h-64 md:h-96 bg-gradient-to-br from-aha-red to-transparent rounded-full blur-3xl"
          style={{ opacity: bgAccentOpacity }}
        ></motion.div>

        <motion.div
          className="absolute bottom-1/4 right-1/4 w-64 md:w-96 h-64 md:h-96 bg-gradient-to-tr from-aha-red to-transparent rounded-full blur-3xl"
          style={{ opacity: bgAccentOpacity }}
        ></motion.div>

        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[120%] h-[120%] bg-gradient-radial from-aha-red via-transparent to-transparent"
          style={{ opacity: centerGradientOpacity }}
        ></motion.div>

        {/* Additional animated elements with enhanced effects */}
        <motion.div
          className="absolute -top-[30%] -right-[10%] w-[70%] h-[70%] rounded-full bg-gradient-to-r from-aha-red/30 via-aha-red/10 to-blue-500/5 blur-[120px]"
          style={{
            opacity: rightGradientOpacity
          }}
          animate={{
            rotate: 360,
            scale: [1, 1.1, 1],
          }}
          transition={{
            rotate: {
              duration: isMobile ? 60 : 40,
              repeat: Infinity,
              ease: "linear"
            },
            scale: {
              duration: 15,
              repeat: Infinity,
              repeatType: "reverse"
            }
          }}
        />

        <motion.div
          className="absolute -bottom-[30%] -left-[10%] w-[70%] h-[70%] rounded-full bg-gradient-to-r from-blue-500/5 via-aha-red/10 to-aha-red/30 blur-[120px]"
          style={{
            opacity: leftGradientOpacity
          }}
          animate={{
            rotate: -360,
            scale: [1, 1.2, 1],
          }}
          transition={{
            rotate: {
              duration: isMobile ? 70 : 50,
              repeat: Infinity,
              ease: "linear"
            },
            scale: {
              duration: 20,
              repeat: Infinity,
              repeatType: "reverse"
            }
          }}
        />

        {/* Additional accent elements that appear on scroll */}
        <motion.div
          className="absolute top-1/3 right-1/3 w-40 h-40 md:w-60 md:h-60 bg-aha-red/20 rounded-full blur-3xl"
          style={{
            opacity: accentOpacity,
            scale: accentScale
          }}
        />
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 relative z-10 py-12 md:py-20">
        <div className="max-w-5xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-6 md:mb-8 cursor-pointer"
            onClick={() => {
 window.location.href = 'https://site.millennialbusinessinnovations.com/';
            }}
          >
            <Badge className="bg-white/10 hover:bg-white/20 text-white border-none py-1.5 px-6 mb-6 md:mb-8 font-medium mx-auto block w-fit animate-fade-in-up text-sm">
              {subtitle}
            </Badge>
          </motion.div>

          {/* Enhanced typography with zoom effect on scroll */}
          <motion.div
            style={{
              scale: titleScale,
              y: titleY
            }}
          >
            <motion.h1
              ref={titleRef}
              className="text-4xl md:text-6xl lg:text-8xl font-extrabold text-center mb-8 md:mb-10 tracking-tight leading-tight"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <span className="text-white">We Make Building Your</span>{' '}
              <span className="text-aha-red">Business Easy</span>
            </motion.h1>
          </motion.div>

          <motion.p
            className="text-center text-gray-300 mb-12 md:mb-16 max-w-3xl mx-auto text-base md:text-xl leading-relaxed"
            style={{
              opacity: paragraphOpacity,
              y: paragraphY
            }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            One simple platform to grow faster without tech headaches.
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row justify-center gap-4 animate-fade-in-up"
            style={{ animationDelay: '0.5s' }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            {isMobile ? (
              <>
                <Button
                  size="default"
                  className="bg-gradient-to-br from-aha-red/90 to-aha-darkred text-white px-6 py-5 rounded-xl border border-white/10 transition-all duration-300 w-auto mx-auto"
                  onClick={() => navigate(ctaLink)}
                >
                  <span className="flex items-center gap-2 font-medium">
                    👉 Get Started Free
                    <ArrowRight className="w-5 h-5" />
                  </span>
                </Button>

                <Button
                  size="default"
                  variant="outline"
                  className="bg-white/5 backdrop-blur-sm border border-white/10 text-white hover:bg-white/10 px-6 py-5 rounded-xl transition-all duration-300 w-auto mx-auto"
                  onClick={() => navigate('/features')}
                >
                  <span className="flex items-center gap-2 font-medium">
                  Learn more →
                  </span>
                </Button>
              </>
            ) : (
              <>
                <Button
                  size="lg"
                  className="bg-gradient-to-br from-aha-red/90 to-aha-darkred text-white text-lg px-8 py-6 rounded-xl border border-white/10 hover:border-aha-red/30 transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/20 group"
                  onClick={() => navigate(ctaLink)}
                >
                  <span className="flex items-center gap-2 font-medium">
                    👉 Get Started Free
                    <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                  </span>
                </Button>

                <Button
                  size="lg"
                  variant="outline"
                  className="bg-white/5 backdrop-blur-sm border border-white/10 text-white hover:bg-white/10 text-lg px-8 py-6 rounded-xl transition-all duration-300 hover:border-aha-red/30 hover:shadow-xl hover:shadow-aha-red/10"
                  onClick={() => navigate('/features')}
                >
                  <span className="flex items-center gap-2 font-medium">
                     Learn More
                  </span>
                </Button>
              </>
            )}
          </motion.div>

          {/* Feature highlights with enhanced zoom effects */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 mt-12 md:mt-20"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            style={{
              scale: featureCardScale,
            }}
          >
            {isMobile ? (
              // Simplified mobile feature cards with minimal animations for better performance
              <>
                <div className="bg-gradient-to-br from-aha-red/20 to-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-5 shadow-md">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 rounded-lg bg-aha-red/30 flex items-center justify-center text-white">
                      <Zap size={22} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-white text-base">Automation</h3>
                      <p className="text-gray-300 text-sm">Smart workflows for repetitive tasks</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-aha-red/20 to-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-5 shadow-md">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 rounded-lg bg-aha-red/30 flex items-center justify-center text-white">
                      <Code size={22} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-white text-base">No-Code Builder</h3>
                      <p className="text-gray-300 text-sm">Create without coding skills</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-aha-red/20 to-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-5 shadow-md">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 rounded-lg bg-aha-red/30 flex items-center justify-center text-white">
                      <Sparkles size={22} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-white text-base">AI-Powered</h3>
                      <p className="text-gray-300 text-sm">Intelligent business insights</p>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              // Desktop feature cards with enhanced zoom effects
              <>
                <motion.div
                  className="bg-gradient-to-br from-aha-red/20 to-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-8 h-full transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/20 hover:border-aha-red/30 group"
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  whileHover={{ scale: 1.03 }}
                  style={{
                    scale: featureCard1Scale,
                  }}
                >
                  <div className="w-14 h-14 rounded-lg bg-aha-red/30 flex items-center justify-center text-white mb-5 group-hover:bg-aha-red/40 transition-all duration-300">
                    <Zap size={26} />
                  </div>
                  <h3 className="font-bold text-white text-xl mb-3">Automation</h3>
                  <p className="text-gray-300 text-base">Save time with smart workflows that handle repetitive tasks automatically</p>
                </motion.div>

                <motion.div
                  className="bg-gradient-to-br from-aha-red/20 to-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-8 h-full transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/20 hover:border-aha-red/30 group"
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  whileHover={{ scale: 1.03 }}
                  style={{
                    scale: featureCard2Scale,
                  }}
                >
                  <div className="w-14 h-14 rounded-lg bg-aha-red/30 flex items-center justify-center text-white mb-5 group-hover:bg-aha-red/40 transition-all duration-300">
                    <Code size={26} />
                  </div>
                  <h3 className="font-bold text-white text-xl mb-3">No-Code Builder</h3>
                  <p className="text-gray-300 text-base">Create powerful websites and applications without any coding skills</p>
                </motion.div>

                <motion.div
                  className="bg-gradient-to-br from-aha-red/20 to-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-8 h-full transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/20 hover:border-aha-red/30 group"
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  whileHover={{ scale: 1.03 }}
                  style={{
                    scale: featureCard3Scale,
                  }}
                >
                  <div className="w-14 h-14 rounded-lg bg-aha-red/30 flex items-center justify-center text-white mb-5 group-hover:bg-aha-red/40 transition-all duration-300">
                    <Sparkles size={26} />
                  </div>
                  <h3 className="font-bold text-white text-xl mb-3">AI-Powered</h3>
                  <p className="text-gray-300 text-base">Leverage intelligent insights and assistance to optimize your business</p>
                </motion.div>
              </>
            )}
          </motion.div>

          {/* Dashboard Preview with Enhanced Zoom Effects */}
          <motion.div
            className="mt-16 md:mt-24 relative"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            style={{
              scale: dashboardScale,
              y: dashboardY
            }}
          >
            <div className="relative group">
              {/* Enhanced glow effect */}
              <motion.div
                className="absolute -inset-1 bg-gradient-to-br from-aha-red/30 to-black/40 rounded-xl blur-xl"
                style={{
                  opacity: dashboardGlowOpacity
                }}
              ></motion.div>

              <div className="relative w-full rounded-xl border border-white/10 overflow-hidden transition-all duration-500 group-hover:border-aha-red/30 group-hover:shadow-xl group-hover:shadow-aha-red/30 z-10">
                {/* Mockup frame overlay */}
                <div className="absolute top-0 left-0 right-0 h-6 md:h-8 bg-black/50 backdrop-blur-sm z-20 flex items-center px-2 md:px-4">
                  <div className="flex gap-1 md:gap-2">
                    <div className="w-2 h-2 md:w-3 md:h-3 rounded-full bg-red-500"></div>
                    <div className="w-2 h-2 md:w-3 md:h-3 rounded-full bg-yellow-500"></div>
                    <div className="w-2 h-2 md:w-3 md:h-3 rounded-full bg-green-500"></div>
                  </div>
                  <div className="text-[10px] md:text-xs text-white/70 mx-auto">AHA-Innovations Platform</div>
                </div>

                {/* Image with zoom effect on scroll */}
                <motion.div
                  style={{
                    scale: dashboardImageScale
                  }}
                >
                  <img
                    src="/AHA-innovations-showcase-.gif"
                    alt="AHA-Innovations Dashboard"
                    className="w-full relative z-10"
                    loading="lazy"
                  />
                </motion.div>
              </div>

              {/* Enhanced animated elements on the dashboard */}
              {!isMobile && (
                <>
                  <motion.div
                    className="absolute top-10 right-10 w-32 h-32 bg-aha-red/20 rounded-full blur-xl"
                    style={{
                      opacity: topGlowOpacity
                    }}
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.3, 0.6, 0.3]
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                  />
                  <motion.div
                    className="absolute bottom-20 left-20 w-24 h-24 bg-aha-red/20 rounded-full blur-xl"
                    style={{
                      opacity: bottomGlowOpacity
                    }}
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [0.2, 0.5, 0.2]
                    }}
                    transition={{
                      duration: 5,
                      repeat: Infinity,
                      repeatType: "reverse",
                      delay: 1
                    }}
                  />

                  {/* Additional accent elements */}
                  <motion.div
                    className="absolute top-1/3 left-1/4 w-16 h-16 bg-aha-red/15 rounded-full blur-xl"
                    style={{
                      opacity: centerGlowOpacity
                    }}
                    animate={{
                      scale: [1, 1.4, 1],
                      opacity: [0.1, 0.4, 0.1]
                    }}
                    transition={{
                      duration: 6,
                      repeat: Infinity,
                      repeatType: "reverse",
                      delay: 2
                    }}
                  />
                </>
              )}

              {/* Simplified mobile-specific glow effect */}
              {isMobile && (
                <div
                  className="absolute inset-0 bg-gradient-to-br from-aha-red/20 via-transparent to-transparent rounded-xl opacity-40"
                ></div>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ModernHero;
