import React from 'react';
import { motion } from 'framer-motion';
import { Badge } from '../ui/badge';
import Tilt3D from '../anim/Tilt3D';
import {
  Globe,
  MessageSquare,
  Users,
  Zap,
} from 'lucide-react';

interface ServiceCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay?: number;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ icon, title, description, delay = 0 }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: delay * 0.1 }}
      viewport={{ once: true, margin: "-50px" }}
    >
      <Tilt3D className="h-full" intensity={5}>
        <div className="bg-gradient-to-br from-aha-red/10 to-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-6 h-full transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/20 hover:border-aha-red/30 group">
          <div className="w-14 h-14 rounded-lg bg-aha-red/20 flex items-center justify-center mb-4 text-white transition-transform duration-300 group-hover:scale-110 group-hover:bg-aha-red/30">
            {icon}
          </div>
          <h3 className="text-xl font-bold mb-2 text-white group-hover:text-aha-red transition-colors duration-300">{title}</h3>
          <p className="text-gray-300">{description}</p>
        </div>
      </Tilt3D>
    </motion.div>
  );
};

const ServicesSection: React.FC = () => {
  const services = [
    {
      icon: <Globe size={28} />,
      title: "Website & Funnel Builder",
      description: "Build your site fast—no code needed.",
    },
    {
      icon: <Users size={28} />,
      title: "CRM & Email Tools",
      description: "Stay in touch, automate follow-ups.",
    },
    {
      icon: <MessageSquare size={28} />,
      title: "Sales Funnels",
      description: "Guide leads from click to client.",
    },
    {
      icon: <Zap size={28} />,
      title: "Automation & Analytics",
      description: "Let your system run while you sleep.",
    },
  ];

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-[#0F0F0F]"></div>

      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute top-1/4 right-0 w-1/2 h-1/2 bg-gradient-to-l from-blue-500/10 via-purple-500/10 to-transparent rounded-full blur-[120px]"
          animate={{
            x: [0, 50, 0],
            opacity: [0.4, 0.6, 0.4],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
        <motion.div
          className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-gradient-to-r from-aha-red/10 via-purple-500/10 to-transparent rounded-full blur-[120px]"
          animate={{
            x: [0, -50, 0],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            repeatType: "reverse",
            delay: 5
          }}
        />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[120%] h-[120%] bg-gradient-radial from-aha-red/10 via-transparent to-transparent opacity-40"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="bg-aha-red/20 hover:bg-aha-red/30 text-aha-red border-none mb-6 py-1.5 px-4 mx-auto block w-fit font-medium">
            Our Services
          </Badge>
          <h2 className="text-4xl md:text-5xl font-extrabold mb-6 tracking-tight">
            What You <span className="text-aha-red">Get</span>
          </h2>
          <p className="text-center text-gray-400 max-w-3xl mx-auto text-lg">
            Everything you need to build, launch, and grow your business in one simple platform.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <ServiceCard
              key={index}
              icon={service.icon}
              title={service.title}
              description={service.description}
              delay={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
