import React from 'react';
import { motion } from 'framer-motion';
import Logo from '../Logo';
import { Link } from 'react-router-dom';
import { Facebook, Twitter, Instagram, Linkedin, Github } from 'lucide-react';

const ModernFooter: React.FC = () => {
  return (
    <footer className="py-16 relative overflow-hidden" style={{ background: '#0F0F0F' }}>
      {/* Animated border */}
      <motion.div
        className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-aha-red/30 via-blue-500/30 to-purple-500/30"
        animate={{
          opacity: [0.3, 0.6, 0.3],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          repeatType: "reverse"
        }}
      />

      <div className="container mx-auto px-4 relative z-10">
        <div className="grid md:grid-cols-4 gap-12">
          <div className="md:col-span-1">
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <Logo />
            </motion.div>
            <p className="text-gray-400 mt-4 text-sm">
              AHA-Innovations – The all-in-one platform for business automation, growth, and productivity.
            </p>
          </div>

          <div className="md:col-span-3 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4 text-white">Product</h3>
              <ul className="space-y-3">
                {[
                  { label: "Features", path: "/features" },
                  { label: "Pricing", path: "/pricing" },
                  { label: "Integrations", path: "/integrations" },
                  { label: "Case Studies", path: "/case-studies" },
                  { label: "Documentation", path: "/docs" }
                ].map((link, index) => (
                  <motion.li
                    key={index}
                    whileHover={{ x: 5 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <Link
                      to={link.path}
                      className="text-gray-400 hover:text-white transition-colors duration-300 text-sm"
                    >
                      {link.label}
                    </Link>
                  </motion.li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4 text-white">Company</h3>
              <ul className="space-y-3">
                {[
                  { label: "About Us", path: "/about" },
                  { label: "Careers", path: "/careers" },
                  { label: "Blog", path: "/blog" },
                  { label: "Press", path: "/press" },
                  { label: "Contact", path: "/contact" }
                ].map((link, index) => (
                  <motion.li
                    key={index}
                    whileHover={{ x: 5 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <Link
                      to={link.path}
                      className="text-gray-400 hover:text-white transition-colors duration-300 text-sm"
                    >
                      {link.label}
                    </Link>
                  </motion.li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4 text-white">Legal</h3>
              <ul className="space-y-3">
                {[
                  { label: "Terms of Service", path: "/terms" },
                  { label: "Privacy Policy", path: "/privacy" },
                  { label: "Cookie Policy", path: "/cookies" },
                  { label: "GDPR", path: "/gdpr" },
                  { label: "Security", path: "/security" }
                ].map((link, index) => (
                  <motion.li
                    key={index}
                    whileHover={{ x: 5 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <Link
                      to={link.path}
                      className="text-gray-400 hover:text-white transition-colors duration-300 text-sm"
                    >
                      {link.label}
                    </Link>
                  </motion.li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        <div className="mt-16 pt-8 border-t border-white/10 flex flex-col md:flex-row justify-between items-center gap-6">
          <div className="text-gray-500 text-sm">
            © {new Date().getFullYear()} AHA-Innovations. All rights reserved.
          </div>

          <div className="flex gap-4">
            {[
              { icon: <Facebook size={16} />, label: "Facebook" },
              { icon: <Twitter size={16} />, label: "Twitter" },
              { icon: <Instagram size={16} />, label: "Instagram" },
              { icon: <Linkedin size={16} />, label: "LinkedIn" },
              { icon: <Github size={16} />, label: "GitHub" }
            ].map((social, index) => (
              <motion.a
                key={index}
                href="#"
                aria-label={social.label}
                className="w-8 h-8 rounded-full bg-white/5 flex items-center justify-center text-gray-400 hover:text-white"
                whileHover={{
                  scale: 1.2,
                  backgroundColor: "rgba(255,255,255,0.1)"
                }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 10
                }}
              >
                {social.icon}
              </motion.a>
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default ModernFooter;
