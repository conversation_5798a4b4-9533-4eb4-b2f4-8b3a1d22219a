
import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { LucideIcon } from 'lucide-react';

interface SocialButtonProps {
  icon: LucideIcon;
  label: string;
  onClick: () => void;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  className?: string;
}

const SocialButton: React.FC<SocialButtonProps> = ({
  icon: Icon,
  label,
  onClick,
  variant = 'outline',
  className,
}) => {
  return (
    <Button
      variant={variant}
      className={cn("w-full flex items-center justify-center gap-2", className)}
      onClick={onClick}
    >
      <Icon className="h-5 w-5" />
      <span>{label}</span>
    </Button>
  );
};

export default SocialButton;
