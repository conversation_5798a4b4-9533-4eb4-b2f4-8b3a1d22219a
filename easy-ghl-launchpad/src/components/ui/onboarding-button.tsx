import React from 'react';
import { cn } from '@/lib/utils';

interface OnboardingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary';
  fullWidth?: boolean;
  children: React.ReactNode;
}

const OnboardingButton = React.forwardRef<HTMLButtonElement, OnboardingButtonProps>(
  ({ className, variant = 'primary', fullWidth = true, children, ...props }, ref) => {
    return (
      <button
        className={cn(
          "rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-blue-500",
          "py-3 px-4 text-center",
          fullWidth ? "w-full" : "",
          variant === 'primary' 
            ? "bg-blue-600 hover:bg-blue-700 text-white" 
            : "bg-gray-700 hover:bg-gray-600 text-white",
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </button>
    );
  }
);

OnboardingButton.displayName = 'OnboardingButton';

export { OnboardingButton };
