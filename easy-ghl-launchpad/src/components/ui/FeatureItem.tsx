import React from 'react';
import { motion } from 'framer-motion';

interface FeatureItemProps {
  text: string;
}

const FeatureItem: React.FC<FeatureItemProps> = ({ text }) => {
  return (
    <motion.li
      className="flex items-start"
      initial={{ opacity: 0, x: -20 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
    >
      <span className="text-aha-red mr-2 mt-1">✓</span>
      <span className="text-gray-300">{text}</span>
    </motion.li>
  );
};

export default FeatureItem;
