import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface GlassBackgroundProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  variant?: 'default' | 'dark' | 'red' | 'blue' | 'purple';
  animated?: boolean;
  blobs?: boolean;
  grid?: boolean;
  noise?: boolean;
  className?: string;
}

const GlassBackground: React.FC<GlassBackgroundProps> = ({
  children,
  variant = 'default',
  animated = true,
  blobs = true,
  grid = false,
  noise = true,
  className,
  ...props
}) => {
  return (
    <div className={cn('relative overflow-hidden', className)} {...props}>
      {/* Base background */}
      <div className="absolute inset-0 bg-aha-dark"></div>
      
      {/* Optional noise texture */}
      {noise && (
        <div 
          className="absolute inset-0 opacity-[0.03] pointer-events-none z-0" 
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
            backgroundSize: '200px'
          }}
        />
      )}
      
      {/* Optional grid pattern */}
      {grid && (
        <div 
          className="absolute inset-0 opacity-[0.03] pointer-events-none z-0" 
          style={{
            backgroundImage: `linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px), 
                              linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px)`,
            backgroundSize: '40px 40px'
          }}
        />
      )}
      
      {/* Animated blobs */}
      {blobs && animated && (
        <>
          <motion.div
            className={cn(
              "absolute -top-1/4 right-1/4 w-1/2 h-1/2 rounded-full blur-[150px] opacity-20",
              {
                'bg-gradient-to-br from-aha-red/30 via-purple-500/20 to-transparent': variant === 'red' || variant === 'default',
                'bg-gradient-to-br from-blue-500/30 via-purple-500/20 to-transparent': variant === 'blue',
                'bg-gradient-to-br from-purple-500/30 via-blue-500/20 to-transparent': variant === 'purple',
                'bg-gradient-to-br from-white/10 via-white/5 to-transparent': variant === 'dark',
              }
            )}
            animate={{
              x: [0, 50, 0],
              y: [0, 30, 0],
              scale: [1, 1.1, 1],
              opacity: [0.15, 0.25, 0.15],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }}
          />
          
          <motion.div
            className={cn(
              "absolute bottom-0 left-1/4 w-1/2 h-1/2 rounded-full blur-[150px] opacity-20",
              {
                'bg-gradient-to-tr from-blue-500/30 via-purple-500/20 to-transparent': variant === 'blue' || variant === 'default',
                'bg-gradient-to-tr from-aha-red/30 via-purple-500/20 to-transparent': variant === 'red',
                'bg-gradient-to-tr from-purple-500/30 via-blue-500/20 to-transparent': variant === 'purple',
                'bg-gradient-to-tr from-white/10 via-white/5 to-transparent': variant === 'dark',
              }
            )}
            animate={{
              x: [0, -50, 0],
              y: [0, -30, 0],
              scale: [1, 1.1, 1],
              opacity: [0.15, 0.25, 0.15],
            }}
            transition={{
              duration: 30,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut",
              delay: 5
            }}
          />
        </>
      )}
      
      {/* Static blobs */}
      {blobs && !animated && (
        <>
          <div
            className={cn(
              "absolute -top-1/4 right-1/4 w-1/2 h-1/2 rounded-full blur-[150px] opacity-20",
              {
                'bg-gradient-to-br from-aha-red/30 via-purple-500/20 to-transparent': variant === 'red' || variant === 'default',
                'bg-gradient-to-br from-blue-500/30 via-purple-500/20 to-transparent': variant === 'blue',
                'bg-gradient-to-br from-purple-500/30 via-blue-500/20 to-transparent': variant === 'purple',
                'bg-gradient-to-br from-white/10 via-white/5 to-transparent': variant === 'dark',
              }
            )}
          />
          
          <div
            className={cn(
              "absolute bottom-0 left-1/4 w-1/2 h-1/2 rounded-full blur-[150px] opacity-20",
              {
                'bg-gradient-to-tr from-blue-500/30 via-purple-500/20 to-transparent': variant === 'blue' || variant === 'default',
                'bg-gradient-to-tr from-aha-red/30 via-purple-500/20 to-transparent': variant === 'red',
                'bg-gradient-to-tr from-purple-500/30 via-blue-500/20 to-transparent': variant === 'purple',
                'bg-gradient-to-tr from-white/10 via-white/5 to-transparent': variant === 'dark',
              }
            )}
          />
        </>
      )}
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

export { GlassBackground };
