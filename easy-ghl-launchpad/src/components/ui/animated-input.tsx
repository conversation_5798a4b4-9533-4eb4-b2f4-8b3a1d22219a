import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useWizard } from "./step-wizard";

interface AnimatedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  containerClassName?: string;
}

const AnimatedInput = React.forwardRef<HTMLInputElement, AnimatedInputProps>(
  ({ className, label, error, containerClassName, onChange, onFocus, onBlur, ...props }, ref) => {
    const [isFocused, setIsFocused] = useState(false);
    const [hasValue, setHasValue] = useState(false);
    const { setIsTyping } = useWizard();
    const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout | null>(null);

    // Check if the input has a value
    useEffect(() => {
      if (props.value) {
        setHasValue(String(props.value).length > 0);
      }
    }, [props.value]);

    // Handle focus event
    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      setIsTyping(true);
      if (onFocus) onFocus(e);
    };

    // Handle blur event
    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      setIsTyping(false);
      if (onBlur) onBlur(e);
    };

    // Handle change event
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setHasValue(e.target.value.length > 0);
      setIsTyping(true);
      
      // Clear previous timeout
      if (typingTimeout) {
        clearTimeout(typingTimeout);
      }
      
      // Set a new timeout to stop typing animation after 1 second of inactivity
      const timeout = setTimeout(() => {
        setIsTyping(false);
      }, 1000);
      
      setTypingTimeout(timeout);
      
      if (onChange) onChange(e);
    };

    // Clean up timeout on unmount
    useEffect(() => {
      return () => {
        if (typingTimeout) {
          clearTimeout(typingTimeout);
        }
      };
    }, [typingTimeout]);

    return (
      <div className={cn("space-y-2", containerClassName)}>
        {label && (
          <motion.label
            className={cn(
              "block text-sm font-medium transition-colors",
              isFocused ? "text-aha-red" : "text-gray-300"
            )}
            animate={{ 
              y: isFocused || hasValue ? 0 : 5,
              scale: isFocused ? 1.02 : 1
            }}
            transition={{ duration: 0.2 }}
          >
            {label}
          </motion.label>
        )}
        <div className="relative">
          <motion.div
            className="absolute inset-0 rounded-md -z-10"
            animate={{
              boxShadow: isFocused 
                ? "0 0 0 2px rgba(234, 56, 76, 0.3), 0 4px 12px rgba(0, 0, 0, 0.2)" 
                : "0 0 0 1px rgba(255, 255, 255, 0.1), 0 2px 4px rgba(0, 0, 0, 0.1)"
            }}
            transition={{ duration: 0.2 }}
          />
          <input
            ref={ref}
            className={cn(
              "flex h-10 w-full rounded-md border bg-gray-800/50 backdrop-blur-sm px-3 py-2 text-base text-white ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-0 disabled:cursor-not-allowed disabled:opacity-50 transition-colors",
              isFocused ? "border-aha-red" : "border-gray-700",
              className
            )}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleChange}
            {...props}
          />
          {isFocused && (
            <motion.div
              className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-aha-red via-blue-500 to-aha-red"
              initial={{ scaleX: 0, opacity: 0 }}
              animate={{ 
                scaleX: 1, 
                opacity: 1,
                backgroundPosition: ["0% 0%", "100% 0%"]
              }}
              transition={{ 
                scaleX: { duration: 0.3 },
                backgroundPosition: { duration: 3, repeat: Infinity, repeatType: "reverse" }
              }}
              style={{ transformOrigin: "left" }}
            />
          )}
        </div>
        {error && (
          <motion.p
            className="text-red-500 text-sm"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            {error}
          </motion.p>
        )}
      </div>
    );
  }
);

AnimatedInput.displayName = "AnimatedInput";

export { AnimatedInput };
