import { useEffect, useRef } from "react";

const GHLContactForm = () => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Load the GHL form script after the component mounts
  useEffect(() => {
    // Create and load the GHL script
    const script = document.createElement("script");
    script.src = "https://link.msgsndr.com/js/form_embed.js";
    script.async = true;
    document.body.appendChild(script);

    // Add a link to our custom CSS for the GHL form
    const styleLink = document.createElement("link");
    styleLink.rel = "stylesheet";
    styleLink.href = "/ghl-form-styles.css";
    document.head.appendChild(styleLink);

    // Cleanup function to remove the script and style when component unmounts
    return () => {
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
      if (document.head.contains(styleLink)) {
        document.head.removeChild(styleLink);
      }
    };
  }, []);

  // Adjust iframe height to match content
  useEffect(() => {
    const adjustHeight = () => {
      if (iframeRef.current && containerRef.current) {
        // Set a minimum height for the iframe
        containerRef.current.style.minHeight = "450px";
      }
    };

    // Initial adjustment
    adjustHeight();

    // Add resize listener
    window.addEventListener("resize", adjustHeight);

    // Cleanup
    return () => {
      window.removeEventListener("resize", adjustHeight);
    };
  }, []);

  return (
    <div ref={containerRef} className="glass-effect p-6 md:p-8 h-full rounded-xl overflow-hidden shadow-2xl border border-gray-800/30 relative">
      {/* Subtle grid overlay */}
      <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-10 z-0"></div>
      
      {/* Form title */}
      <div className="relative z-10 mb-4 text-center">
        <h3 className="text-xl font-bold text-white">Create Your AHA Account</h3>
        <p className="text-gray-300 text-sm mt-1">Fill out the form below to get started</p>
      </div>
      
      <iframe
        ref={iframeRef}
        src="https://calendar.aha-innovations.com/widget/form/YN86bt2SlADr1BfnDEkq?transparentBackground=true&hideColumns=true&backgroundColor=transparent"
        style={{
          width: "100%",
          height: "100%",
          border: "none",
          borderRadius: "8px",
          minHeight: "426px",
          backgroundColor: "transparent"
        }}
        id="inline-YN86bt2SlADr1BfnDEkq"
        data-layout="{'id':'INLINE'}"
        data-trigger-type="alwaysShow"
        data-trigger-value=""
        data-activation-type="alwaysActivated"
        data-activation-value=""
        data-deactivation-type="neverDeactivate"
        data-deactivation-value=""
        data-form-name="AHA Signup Form"
        data-height="700"
        data-layout-iframe-id="inline-YN86bt2SlADr1BfnDEkq"
        data-form-id="YN86bt2SlADr1BfnDEkq"
        data-custom-css-url="/ghl-form-styles.css"
        title="Sign Up Form"
      />
    </div>
  );
};

export default GHLContactForm;
