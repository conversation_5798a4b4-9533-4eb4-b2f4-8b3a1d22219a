import { handleTestGHLIntegration } from './api';

// Simple server-side request handler
export async function handleRequest(request: Request) {
  const url = new URL(request.url);
  const path = url.pathname;

  // Handle API routes
  if (path === '/api/test-ghl-integration' && request.method === 'POST') {
    return handleTestGHLIntegration(request);
  }

  // Return 404 for unknown routes
  return new Response('Not Found', { status: 404 });
}
