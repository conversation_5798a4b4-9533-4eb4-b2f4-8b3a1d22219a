/**
 * This file provides error handling utilities to catch and fix common JavaScript errors
 */

/**
 * Initializes error handling for common JavaScript errors
 */
export const initErrorHandler = () => {
  // Create a global error handler
  window.addEventListener('error', (event) => {
    // Check for the specific "there is not defined" error
    if (event.message.includes('there is not defined')) {
      // Prevent the error from propagating
      event.preventDefault();
      console.warn('Caught "there is not defined" error and prevented it from crashing the app');
      
      // You can add additional handling here if needed
      return true;
    }
    
    return false;
  });
  
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && event.reason.message && 
        event.reason.message.includes('there is not defined')) {
      // Prevent the error from propagating
      event.preventDefault();
      console.warn('Caught "there is not defined" error in promise and prevented it from crashing the app');
      
      return true;
    }
    
    return false;
  });
};
