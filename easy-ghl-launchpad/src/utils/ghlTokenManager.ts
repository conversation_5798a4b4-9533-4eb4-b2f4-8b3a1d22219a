// GoHighLevel OAuth Token Manager
// This file manages the OAuth tokens for GoHighLevel API

import { createClient } from '@supabase/supabase-js';

// Import Supabase client from the centralized client
import { supabase } from './supabaseClient';

// GHL API base URL
const GHL_API_URL = 'https://services.leadconnectorhq.com';

/**
 * Get GHL credentials from Supabase Edge Function
 */
const getGHLCredentials = async () => {
  try {
    const { data, error } = await supabase.functions.invoke('get-ghl-credentials', {
      body: {},
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error getting GHL credentials:', error);
    throw error;
  }
};

/**
 * Exchange authorization code for access token
 * This is used in the initial OAuth flow
 */
export const exchangeAuthCodeForToken = async (code: string, redirectUri: string) => {
  try {
    // Get client credentials
    const { clientId, clientSecret } = await getGHLCredentials();

    // Exchange code for tokens
    const response = await fetch(`${GHL_API_URL}/oauth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: clientId,
        client_secret: clientSecret,
        code: code,
        grant_type: 'authorization_code',
        redirect_uri: redirectUri,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to exchange authorization code: ${errorText}`);
    }

    const tokenData = await response.json();

    // Calculate expiration time
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + tokenData.expires_in);

    // Store tokens in database
    await storeTokens({
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token,
      expires_at: expiresAt.toISOString(),
      token_type: tokenData.token_type,
    });

    return tokenData;
  } catch (error) {
    console.error('Error exchanging auth code for token:', error);
    throw error;
  }
};

/**
 * Refresh the access token using the refresh token
 */
export const refreshAccessToken = async (refreshToken: string) => {
  try {
    // Get client credentials
    const { clientId, clientSecret } = await getGHLCredentials();

    // Exchange refresh token for new access token
    const response = await fetch(`${GHL_API_URL}/oauth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: clientId,
        client_secret: clientSecret,
        refresh_token: refreshToken,
        grant_type: 'refresh_token',
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to refresh access token: ${errorText}`);
    }

    const tokenData = await response.json();

    // Calculate expiration time
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + tokenData.expires_in);

    // Store tokens in database
    await storeTokens({
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token,
      expires_at: expiresAt.toISOString(),
      token_type: tokenData.token_type,
    });

    return tokenData;
  } catch (error) {
    console.error('Error refreshing access token:', error);
    throw error;
  }
};

/**
 * Store OAuth tokens in the database
 */
const storeTokens = async (tokenData: {
  access_token: string;
  refresh_token: string;
  expires_at: string;
  token_type: string;
}) => {
  try {
    // Store in ghl_tokens table - upsert to update if exists
    const { error } = await supabase
      .from('ghl_tokens')
      .upsert({
        id: 1, // Use a fixed ID for the agency token
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token,
        expires_at: tokenData.expires_at,
        token_type: tokenData.token_type,
        updated_at: new Date().toISOString(),
      });

    if (error) throw error;
  } catch (error) {
    console.error('Error storing tokens:', error);
    throw error;
  }
};

/**
 * Get a valid access token
 * This will automatically refresh the token if it's expired
 */
export const getValidAccessToken = async (): Promise<string> => {
  try {
    // Get the stored token
    const { data, error } = await supabase
      .from('ghl_tokens')
      .select('*')
      .eq('id', 1)
      .single();

    if (error) throw error;

    if (!data) {
      throw new Error('No GHL tokens found. Please complete the OAuth flow first.');
    }

    // Check if token is expired
    const expiresAt = new Date(data.expires_at);
    const now = new Date();

    // If token expires in less than 5 minutes, refresh it
    const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);

    if (expiresAt < fiveMinutesFromNow) {
      console.log('Access token expired or expiring soon, refreshing...');
      const newTokenData = await refreshAccessToken(data.refresh_token);
      return newTokenData.access_token;
    }

    // Token is still valid
    return data.access_token;
  } catch (error) {
    console.error('Error getting valid access token:', error);
    throw error;
  }
};
