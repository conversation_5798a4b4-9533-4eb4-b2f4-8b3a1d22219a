// Utility functions for autofilling onboarding forms with user data
import { getGHLCredentials } from './ghlClient';

/**
 * Interface for user profile data
 */
export interface UserProfile {
  id: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  company_name?: string;
  phone?: string;
  city?: string;
  state?: string;
  country?: string;
  role?: string;
  company_size?: string;
  referral_source?: string;
  ghl_account_id?: string;
  ghl_location_id?: string;
  ghl_user_id?: string;
  ghl_auth_code?: string;
  user_metadata?: any;
  [key: string]: any; // Allow for additional properties
}

/**
 * Interface for onboarding form data
 */
export interface OnboardingFormData {
  firstName: string;
  lastName: string;
  companyName: string;
  phone?: string;
  city?: string;
  state?: string;
  country?: string;
  role?: string;
  company_size?: string;
  referral_source?: string;
  [key: string]: any; // Allow for additional properties
}

/**
 * Fetches user profile data from the get-ghl-credentials edge function
 * and formats it for use in the onboarding wizard
 * 
 * @param userId - The user's ID
 * @param email - The user's email (optional, used as fallback if userId is not provided)
 * @returns A promise that resolves to the formatted onboarding form data
 */
export const fetchUserProfileForOnboarding = async (
  userId?: string,
  email?: string
): Promise<OnboardingFormData> => {
  try {
    console.log('Fetching user profile for onboarding:', { userId, email });
    
    // Call the edge function to get the user profile
    const response = await getGHLCredentials(userId);
    
    // Extract the user profile from the response
    const userProfile = response?.userProfile;
    
    console.log('User profile from edge function:', userProfile);
    
    if (!userProfile) {
      console.warn('No user profile found in edge function response');
      return createDefaultFormData(email);
    }
    
    // Format the user profile data for the onboarding form
    return formatUserProfileForOnboarding(userProfile);
  } catch (error) {
    console.error('Error fetching user profile for onboarding:', error);
    // Return default form data if there's an error
    return createDefaultFormData(email);
  }
};

/**
 * Formats a user profile for use in the onboarding wizard
 * 
 * @param profile - The user profile data
 * @returns The formatted onboarding form data
 */
export const formatUserProfileForOnboarding = (profile: UserProfile): OnboardingFormData => {
  // Extract user metadata for convenience
  const metadata = profile.user_metadata || {};
  
  // Create the form data object with all available information
  const formData: OnboardingFormData = {
    firstName: profile.first_name || metadata.first_name || metadata.name?.split(' ')[0] || '',
    lastName: profile.last_name || metadata.last_name || (metadata.name ? metadata.name.split(' ').slice(1).join(' ') : '') || '',
    companyName: profile.company_name || metadata.company_name || (metadata.full_name ? `${metadata.full_name}'s Agency` : ''),
    phone: profile.phone || metadata.phone || '',
    city: profile.city || metadata.city || '',
    state: profile.state || metadata.state || '',
    country: profile.country || metadata.country || 'United States',
    role: profile.role || metadata.role || '',
    company_size: profile.company_size || metadata.company_size || '',
    referral_source: profile.referral_source || metadata.referral_source || ''
  };
  
  console.log('Formatted onboarding form data:', formData);
  
  return formData;
};

/**
 * Creates default form data with minimal information
 * 
 * @param email - The user's email (optional)
 * @returns Default onboarding form data
 */
const createDefaultFormData = (email?: string): OnboardingFormData => {
  // Create minimal form data with just the email if available
  const defaultData: OnboardingFormData = {
    firstName: '',
    lastName: '',
    companyName: '',
    country: 'United States'
  };
  
  // If email is provided, try to extract a name from it
  if (email) {
    const emailParts = email.split('@')[0].split('.');
    if (emailParts.length > 1) {
      // If email looks like "<EMAIL>"
      defaultData.firstName = capitalizeFirstLetter(emailParts[0]);
      defaultData.lastName = capitalizeFirstLetter(emailParts[1]);
    } else if (emailParts.length === 1) {
      // If email looks like "<EMAIL>"
      defaultData.firstName = capitalizeFirstLetter(emailParts[0]);
    }
    
    // Create a default company name based on the email
    if (defaultData.firstName) {
      defaultData.companyName = `${defaultData.firstName}'s Agency`;
    }
  }
  
  return defaultData;
};

/**
 * Capitalizes the first letter of a string
 * 
 * @param str - The string to capitalize
 * @returns The capitalized string
 */
const capitalizeFirstLetter = (str: string): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};
