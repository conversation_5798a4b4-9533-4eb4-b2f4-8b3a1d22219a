
import axios from 'axios';

const GHL_CLIENT_ID = process.env.GHL_CLIENT_ID;
const GHL_CLIENT_SECRET = process.env.GHL_CLIENT_SECRET;
const GHL_REDIRECT_URI = process.env.GHL_REDIRECT_URI;

if (!GHL_CLIENT_ID || !GHL_CLIENT_SECRET || !GHL_REDIRECT_URI) {
  console.warn('GoHighLevel OAuth credentials are not fully configured.');
}

export const initiateGHLAuth = () => {
  const authUrl = `https://app.gohighlevel.com/oauth/authorize?client_id=${GHL_CLIENT_ID}&redirect_uri=${GHL_REDIRECT_URI}&response_type=code&scope=locations.readonly locations.write users.readonly users.write`;
  return authUrl;
};

export const exchangeCodeForToken = async (code: string) => {
  try {
    const tokenResponse = await axios.post('https://app.gohighlevel.com/oauth/token', null, {
      params: {
        client_id: GHL_CLIENT_ID,
        client_secret: GHL_CLIENT_SECRET,
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: GHL_REDIRECT_URI,
      },
    });
    return tokenResponse.data;
  } catch (error: any) {
    console.error('Failed to exchange code for token:', error.response ? error.response.data : error.message);
    throw new Error('Failed to exchange code for token');
  }
};

export const refreshAccessToken = async (refreshToken: string) => {
  try {
    const refreshResponse = await axios.post('https://app.gohighlevel.com/oauth/token', null, {
      params: {
        client_id: GHL_CLIENT_ID,
        client_secret: GHL_CLIENT_SECRET,
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
      },
    });
    return refreshResponse.data;
  } catch (error: any) {
    console.error('Failed to refresh access token:', error.response ? error.response.data : error.message);
    throw new Error('Failed to refresh access token');
  }
};

export const getCompanyInfo = async (accessToken: string) => {
  try {
    const companyResponse = await axios.get('https://services.leadconnectorhq.com/company/me', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });
    return companyResponse.data;
  } catch (error: any) {
    console.error('Failed to fetch company info:', error.response ? error.response.data : error.message);
    throw new Error('Failed to fetch company info');
  }
};

export const getLocations = async (accessToken: string) => {
  try {
    const locationsResponse = await axios.get('https://services.leadconnectorhq.com/locations/me', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });
    return locationsResponse.data;
  } catch (error: any) {
    console.error('Failed to fetch locations:', error.response ? error.response.data : error.message);
    throw new Error('Failed to fetch locations');
  }
};

export const createUserAndSubaccount = async ({
  email,
  firstName,
  lastName,
  companyName,
  phone,
  city,
  state,
  country,
}: {
  email: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  phone?: string;
  city?: string;
  state?: string;
  country?: string;
}) => {
  try {
    // Use your actual access token retrieval method here
    const accessToken = 'YOUR_ACCESS_TOKEN';

    // Create a new location
    const locationData = {
      name: companyName || `${firstName}'s Agency`,
      address: '123 Main St', // Replace with actual address if available
      city: city || 'Anytown',
      state: state || 'CA',
      country: country || 'USA',
      postalCode: '12345', // Replace with actual postal code if available
      timezone: 'America/Los_Angeles', // Replace with actual timezone if available
      phone: phone || '************',
      website: 'https://example.com', // Replace with actual website if available
    };

    const locationResponse = await axios.post('https://services.leadconnectorhq.com/locations', locationData, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    const locationId = locationResponse.data.id;

    // Create a new user
    const userData = {
      firstName,
      lastName,
      email,
      phone: phone || '',
    };

    const userResponse = await axios.post(`https://services.leadconnectorhq.com/locations/${locationId}/users`, userData, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    return {
      location: locationResponse.data,
      user: userResponse.data,
    };
  } catch (error: any) {
    console.error('Failed to create user and subaccount:', error.response ? error.response.data : error.message);
    throw new Error('Failed to create user and subaccount');
  }
};
