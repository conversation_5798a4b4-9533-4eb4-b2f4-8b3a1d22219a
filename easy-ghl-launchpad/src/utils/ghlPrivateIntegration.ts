
import axios from 'axios';

// Function to store the Private Integration token in localStorage
export const storePrivateIntegrationToken = async (token: string): Promise<void> => {
  localStorage.setItem('ghl_private_integration_token', token);
};

// Function to retrieve the Private Integration token from localStorage
export const getPrivateIntegrationToken = async (): Promise<string> => {
  const token = localStorage.getItem('ghl_private_integration_token');
  if (!token) {
    throw new Error('No Private Integration token found. Please set it in the settings.');
  }
  return token;
};

// Function to remove the Private Integration token from localStorage
export const removePrivateIntegrationToken = async (): Promise<void> => {
  localStorage.removeItem('ghl_private_integration_token');
};

// Function to make a request to the GHL API using the Private Integration token
const makeGHLRequest = async (endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE', data?: any) => {
  const token = await getPrivateIntegrationToken();
  const url = `https://services.leadconnectorhq.com${endpoint}`;

  try {
    const response = await axios({
      method,
      url,
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      data,
    });

    return response.data;
  } catch (error: any) {
    console.error(`GHL API request failed for endpoint ${endpoint}:`, error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'GHL API request failed');
  }
};

// Function to create a subaccount in GoHighLevel using the Private Integration token
export const createGHLSubaccountAndUser = async ({
  email,
  firstName,
  lastName,
  companyName,
  phone,
  city,
  state,
  country,
}: {
  email: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  phone?: string;
  city?: string;
  state?: string;
  country?: string;
}) => {
  // Validate required fields
  if (!email || !firstName || !lastName) {
    throw new Error('Missing required fields for creating a GHL subaccount and user.');
  }

  // Construct the location data
  const locationData = {
    name: companyName || `${firstName}'s Agency`,
    email,
    phone: phone || '',
    address: city || '',
    city: city || '',
    state: state || '',
    country: country || '',
    postalCode: '12345', // Default postal code
    timezone: 'America/Los_Angeles', // Default timezone
  };

  // Create the subaccount (location)
  const locationResponse = await makeGHLRequest('/locations', 'POST', locationData);
  const locationId = locationResponse.id;

  if (!locationId) {
    throw new Error('Failed to create GHL subaccount (location).');
  }

  // Construct the user data
  const userData = {
    firstName,
    lastName,
    email,
    phone: phone || '',
  };

  // Invite the user to the subaccount
  const userResponse = await makeGHLRequest(`/locations/${locationId}/users`, 'POST', userData);

  if (!userResponse.id) {
    throw new Error('Failed to create GHL user.');
  }

  return {
    locationId,
    userId: userResponse.id,
  };
};

export default makeGHLRequest;
