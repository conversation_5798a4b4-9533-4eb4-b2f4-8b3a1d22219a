// Utility functions for calling Supabase Edge Functions

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Call a Supabase Edge Function
 * @param functionName - Name of the Edge Function to call
 * @param payload - Data to send to the function
 * @returns Response from the function
 */
export async function callEdgeFunction(functionName: string, payload: any): Promise<any> {
  try {
    console.log(`Calling Edge Function: ${functionName}`, payload);

    // In development, use our proxy to avoid CORS issues
    if (import.meta.env.DEV) {
      const baseUrl = '/functions/v1';
      console.log(`Using development proxy: ${baseUrl}/${functionName}`);

      const response = await fetch(`${baseUrl}/${functionName}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Accept': 'application/json',
        },
        credentials: 'omit',
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        let errorText;
        try {
          const errorJson = await response.json();
          errorText = JSON.stringify(errorJson);
        } catch {
          errorText = await response.text();
        }
        throw new Error(`Failed to call ${functionName}: ${errorText}`);
      }

      const data = await response.json();
      console.log(`Edge Function response (${functionName}):`, data);
      return data;
    } else {
      // In production, use the Supabase SDK
      const { data, error } = await supabase.functions.invoke(functionName, {
        body: payload,
      });

      if (error) {
        console.error(`Edge Function error (${functionName}):`, error);
        throw new Error(error.message || `Failed to call ${functionName}`);
      }

      console.log(`Edge Function response (${functionName}):`, data);
      return data;
    }
  } catch (error) {
    console.error(`Error calling Edge Function (${functionName}):`, error);
    throw error;
  }
}

/**
 * Create a GHL account using the Edge Function
 * @param userData - User data for account creation
 * @returns Response from the function
 */
export async function createGHLAccount(userData: {
  email: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  password?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  timezone?: string;
  currency?: string;
}): Promise<any> {
  return callEdgeFunction('create-ghl-account', userData);
}

/**
 * Exchange GHL authorization code for access token
 * @param code - Authorization code
 * @param redirectUri - Redirect URI used in authorization request
 * @returns Token response
 */
export async function exchangeGHLAuthCode(code: string, redirectUri: string): Promise<any> {
  return callEdgeFunction('exchange-ghl-auth-code', { code, redirectUri });
}

/**
 * Get GHL credentials (client ID only, for security)
 * @returns GHL client ID
 */
export async function getGHLCredentials(): Promise<{ clientId: string }> {
  return callEdgeFunction('get-ghl-credentials', {});
}

/**
 * Get GHL agency token (for admin operations)
 * @returns GHL agency token
 */
export async function getGHLAgencyToken(): Promise<{ token: string }> {
  return callEdgeFunction('get-ghl-agency-token', {});
}
