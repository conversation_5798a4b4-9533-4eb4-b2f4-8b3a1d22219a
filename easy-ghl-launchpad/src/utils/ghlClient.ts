// GoHighLevel client configuration
// This file manages the connection to GoHighLevel API

import { supabase, callEdgeFunction, supabaseUrl, supabaseAnonKey } from './supabaseClient';

/**
 * Generate the GoHighLevel OAuth URL for authentication
 * This follows the OAuth 2.0 flow for GHL Marketplace
 */
export const generateGHLOAuthURL = async () => {
  try {
    // Get credentials from Supabase Edge Function
    const { clientId } = await getGHLCredentials();

    if (!clientId) {
      throw new Error('GHL Client ID not found');
    }

    console.log("Generating GHL OAuth URL with client ID:", clientId);

    // Build the OAuth URL with the required parameters
    const baseURL = 'https://marketplace.gohighlevel.com/oauth/chooselocation';

    // Use localhost for local development, otherwise use production URL
    const isLocalDevelopment = window.location.hostname === 'localhost';
    const redirectURI = isLocalDevelopment
      ? 'http://localhost:8080/ghl-auth-callback'
      : 'https://app.aha-innovations.com/ghl-auth-callback';

    console.log("Using redirect URI:", redirectURI);

    // Define scopes - these should match what you've configured in your GHL Marketplace app
    const scopes = 'conversations/message.readonly conversations/message.write locations.readonly locations.write users.readonly users.write';

    // Generate random state for security
    const state = Math.random().toString(36).substring(2, 15);
    // Store state in localStorage for validation on callback
    localStorage.setItem('ghl_auth_state', state);

    // Construct the OAuth URL according to GHL's documentation
    const oauthURL = `${baseURL}?response_type=code&redirect_uri=${encodeURIComponent(redirectURI)}&client_id=${clientId}&scope=${encodeURIComponent(scopes)}&state=${state}`;

    console.log("Generated OAuth URL:", oauthURL);

    return oauthURL;
  } catch (error) {
    console.error('Error generating GHL OAuth URL:', error);
    throw error;
  }
};

/**
 * Gets the GHL client credentials from Supabase Edge Function
 * This keeps the actual credentials secure on the server-side
 * @param userId Optional user ID to fetch profile data
 */
export const getGHLCredentials = async (userId?: string) => {
  try {
    // Get credentials from Supabase Edge Function using our secure utility
    // If userId is provided, also fetch the user profile
    return await callEdgeFunction('get-ghl-credentials', userId ? { userId } : {});
  } catch (error) {
    console.error('Error getting GHL credentials:', error);
    throw error;
  }
};

/**
 * Checks if a user already exists in GoHighLevel
 * This is the main function used to auto-detect GHL accounts
 */
export const checkGHLUserExists = async (email: string) => {
  try {
    // Call the Supabase Edge Function that will handle GHL API calls securely
    return await callEdgeFunction('check-ghl-user-exists', { email });
  } catch (error) {
    console.error('Error checking if GHL user exists:', error);
    throw error;
  }
};

/**
 * Create a subaccount and user in GoHighLevel using direct API endpoints
 * This is the comprehensive function that follows the Pabbly workflow
 */
export const createGHLSubaccountAndUser = async (userData: {
  email: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  companyName?: string;
  password?: string;
  // Additional fields for better account setup
  city?: string;
  state?: string;
  country?: string;
  phone?: string;
  // New onboarding fields
  role?: string;
  company_size?: string;
  referral_source?: string;
}) => {
  try {
    // First check if user already exists
    const existingUser = await checkGHLUserExists(userData.email);

    if (existingUser?.exists && existingUser?.locationId) {
      // User already exists, return the existing location ID
      return {
        locationId: existingUser.locationId,
        userId: existingUser.userId,
        existing: true
      };
    }

    // Call the Supabase Edge Function that will create both location and user
    const data = await callEdgeFunction('create-ghl-full-account', {
      email: userData.email,
      firstName: userData.firstName || userData.name?.split(' ')[0] || userData.email.split('@')[0],
      lastName: userData.lastName || userData.name?.split(' ').slice(1).join(' ') || '',
      companyName: userData.companyName || userData.name || userData.email.split('@')[0],
      password: userData.password || generateRandomPassword(12),
      // Include additional fields if provided
      city: userData.city || '',
      state: userData.state || '',
      country: userData.country || '',
      phone: userData.phone || '',
      // Include new onboarding fields
      role: userData.role || '',
      company_size: userData.company_size || '',
      referral_source: userData.referral_source || '',
      // Ensure the account is created under the AHA Innovations agency
      parentAgency: 'aha-innovations'
    });

    return data;
  } catch (error) {
    console.error('Error creating GHL account:', error);
    throw error;
  }
};

/**
 * Legacy function maintained for backward compatibility
 */
export const createGHLSubaccount = async (userData: {
  email: string;
  name?: string;
  companyName?: string;
}) => {
  // Forward to the new comprehensive function
  return createGHLSubaccountAndUser(userData);
};

/**
 * Generate a random password for GHL accounts
 * This is used when creating accounts automatically
 */
const generateRandomPassword = (length: number): string => {
  const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_=+";
  let password = "";

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }

  return password;
};
