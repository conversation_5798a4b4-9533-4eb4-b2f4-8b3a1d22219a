
// Setup type definitions for built-in Supabase Runtime APIs
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// GHL API base URL
const GHL_API_URL = 'https://services.leadconnectorhq.com';

serve(async (req: Request) => {
  // CORS headers - Allow specific origins for better security
  const headers = {
    'Access-Control-Allow-Origin': 'http://localhost:8080',  // Specific to your dev server
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Access-Control-Allow-Credentials': 'true',  // Important for authenticated requests
    'Content-Type': 'application/json'
  };

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers });
  }

  try {
    // Parse the request body to get user data
    const userData = await req.json();
    const { email } = userData;

    if (!email) {
      throw new Error('Email is required');
    }

    // Get GHL credentials from environment variables
    // For Agency access, we need the Agency Bearer Token
    // @ts-ignore
    const agencyToken = Deno.env.get('GHL_AGENCY_TOKEN');

    if (!agencyToken) {
      throw new Error('GHL Agency Token not found in environment variables');
    }

    // Use the Agency Bearer Token for authentication
    const accessToken = agencyToken;

    console.log('Using Agency Bearer Token for authentication');

    // No need to get a token since we're using the Agency Bearer Token directly

    // Get GHL company ID from environment variables
    // @ts-ignore
    const companyId = Deno.env.get('GHL_COMPANY_ID');

    if (!companyId) {
      throw new Error('GHL Company ID not found in environment variables');
    }

    // First, try to find the user by email using the users search endpoint
    // Using the endpoint from the documentation: GET /users/search
    const userSearchResponse = await fetch(`${GHL_API_URL}/users/search?companyId=${encodeURIComponent(companyId)}&query=${encodeURIComponent(email)}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Version': '2021-07-28'
      },
    });

    if (!userSearchResponse.ok) {
      const errorText = await userSearchResponse.text();
      console.error(`Failed to search GHL users: ${errorText}`);
      // Continue with location search if user search fails
    } else {
      const userData = await userSearchResponse.json();

      // Check if any users with this email exist
      if (userData && userData.users && userData.users.length > 0) {
        // Filter users by exact email match
        const exactMatch = userData.users.find((user: any) => user.email === email);

        if (exactMatch) {
          // User exists, extract user ID and location IDs if available
          return new Response(JSON.stringify({
            exists: true,
            userId: exactMatch.id,
            locationIds: exactMatch.locationIds || [],
            source: 'user_search',
            count: 1
          }), {
            status: 200,
            headers
          });
        }
      }
    }

    // If no user found, try to find the user by email in sub-accounts (locations)
    // Using the endpoint from the documentation: GET /locations
    const locationSearchResponse = await fetch(`${GHL_API_URL}/api/v1/locations?filter[email]=${encodeURIComponent(email)}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Version': '2021-07-28'
      },
    });

    if (!locationSearchResponse.ok) {
      const errorText = await locationSearchResponse.text();
      throw new Error(`Failed to search GHL locations: ${errorText}`);
    }

    const locationData = await locationSearchResponse.json();

    // Check if any locations with this email exist
    const locationExists = locationData.locations && locationData.locations.length > 0;
    let locationId = null;

    if (locationExists && locationData.locations.length > 0) {
      locationId = locationData.locations[0].id;

      return new Response(JSON.stringify({
        exists: true,
        locationId: locationId,
        source: 'location_search',
        count: locationData.locations.length
      }), {
        status: 200,
        headers
      });
    }

    // If no location found by direct email, try searching for user as a contact
    try {
      // Search for contact with the email using the contacts lookup endpoint
      const contactSearchResponse = await fetch(`${GHL_API_URL}/api/v1/contacts/lookup?email=${encodeURIComponent(email)}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Version': '2021-07-28'
        },
      });

      if (contactSearchResponse.ok) {
        const contactData = await contactSearchResponse.json();

        if (contactData && contactData.contacts && contactData.contacts.length > 0) {
          // Contact exists, extract location ID
          const contactLocationId = contactData.contacts[0].locationId;

          return new Response(JSON.stringify({
            exists: true,
            locationId: contactLocationId,
            source: 'contact_search',
            count: contactData.contacts.length
          }), {
            status: 200,
            headers
          });
        }
      }
    } catch (contactError) {
      console.error('Contact search failed:', contactError);
      // Continue with regular response if contact search fails
    }

    return new Response(JSON.stringify({
      exists: false,
      locationId: null,
      source: 'all_searches',
      count: 0
    }), {
      status: 200,
      headers
    });
  } catch (error) {
    console.error('Error checking GHL user exists:', error);

    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers
    });
  }
})
