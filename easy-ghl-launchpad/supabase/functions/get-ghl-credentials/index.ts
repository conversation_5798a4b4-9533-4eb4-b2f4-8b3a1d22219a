
// Setup type definitions for built-in Supabase Runtime APIs
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// Interface for user profile data
interface UserProfile {
  id: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  company_name?: string;
  phone?: string;
  city?: string;
  state?: string;
  country?: string;
  role?: string;
  company_size?: string;
  referral_source?: string;
  ghl_account_id?: string;
  ghl_location_id?: string;
  ghl_user_id?: string;
  ghl_auth_code?: string;
  [key: string]: any; // Allow for additional properties
}

// This edge function securely returns the GHL credentials and user profile data
// The credentials are stored as secrets in Supabase
serve(async (req: Request) => {
  // CORS headers - Allow all origins for testing
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers });
  }

  try {
    // Get the user ID from the request body if available
    let userId: string | null = null;
    let userEmail: string | null = null;
    let userProfile: UserProfile | null = null;
    let userMetadata: any = null;

    if (req.method === 'POST') {
      const body = await req.json();
      userId = body.userId;
      userEmail = body.email;

      // If userId is provided, fetch the user profile from Supabase
      if (userId) {
        // Create a Supabase client using the service role key
        // @ts-ignore
        const supabaseUrl = Deno.env.get('SUPABASE_URL');
        // @ts-ignore
        const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

        if (supabaseUrl && supabaseServiceKey) {
          const supabase = createClient(supabaseUrl, supabaseServiceKey);

          // Fetch the user profile from profiles table
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', userId)
            .single();

          // Fetch user metadata from auth.users
          const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);

          if (!profileError && profileData) {
            userProfile = profileData as UserProfile;
          }

          if (!userError && userData) {
            userMetadata = userData.user.user_metadata;

            // If we have user metadata but no profile, create a profile object
            if (!userProfile) {
              userProfile = {
                id: userId as string
              };
            }

            // If we have an email from user metadata but not in the profile, add it
            if (userData.user.email && (!userProfile.email || userProfile.email === '')) {
              userProfile.email = userData.user.email;
            }
          }
        }
      } else if (userEmail) {
        // If only email is provided, try to find the user by email
        // @ts-ignore
        const supabaseUrl = Deno.env.get('SUPABASE_URL');
        // @ts-ignore
        const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

        if (supabaseUrl && supabaseServiceKey) {
          const supabase = createClient(supabaseUrl, supabaseServiceKey);

          // Find user by email
          const { data: userData, error: userError } = await supabase.auth.admin.listUsers({
            filters: {
              email: userEmail
            }
          });

          if (!userError && userData && userData.users.length > 0) {
            const user = userData.users[0];
            userId = user.id;
            userMetadata = user.user_metadata;

            // Fetch the user profile
            const { data: profileData, error: profileError } = await supabase
              .from('profiles')
              .select('*')
              .eq('id', userId)
              .single();

            if (!profileError && profileData) {
              userProfile = profileData as UserProfile;
            } else {
              // Create a minimal profile with available data
              userProfile = {
                id: userId as string,
                email: userEmail
              };
            }
          }
        }
      }
    }

    // Return the GHL credentials from environment variables
    // These are set in the Supabase Edge Functions settings
    const credentials = {
      // @ts-ignore
      clientId: Deno.env.get('GHL_CLIENT_ID'),
      // @ts-ignore
      clientSecret: Deno.env.get('GHL_CLIENT_SECRET'),
      // @ts-ignore
      agencyToken: Deno.env.get('GHL_AGENCY_TOKEN'),
      // @ts-ignore
      companyId: Deno.env.get('GHL_COMPANY_ID')
    };

    // Combine profile data with user metadata for a complete user profile
    const enhancedUserProfile = userProfile ? {
      ...userProfile,
      // Fill in missing profile fields from user metadata if available
      first_name: userProfile.first_name || (userMetadata as any)?.first_name || (userMetadata as any)?.name?.split(' ')[0] || '',
      last_name: userProfile.last_name || (userMetadata as any)?.last_name || ((userMetadata as any)?.name ? (userMetadata as any).name.split(' ').slice(1).join(' ') : '') || '',
      company_name: userProfile.company_name || (userMetadata as any)?.company_name || ((userMetadata as any)?.full_name ? `${(userMetadata as any).full_name}'s Agency` : ''),
      role: userProfile.role || (userMetadata as any)?.role || '',
      company_size: userProfile.company_size || (userMetadata as any)?.company_size || '',
      referral_source: userProfile.referral_source || (userMetadata as any)?.referral_source || '',
      // Include user metadata for reference
      user_metadata: userMetadata
    } : null;

    // Include the enhanced user profile in the response if available
    const response = {
      ...credentials,
      userProfile: enhancedUserProfile
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers
    });
  }
});