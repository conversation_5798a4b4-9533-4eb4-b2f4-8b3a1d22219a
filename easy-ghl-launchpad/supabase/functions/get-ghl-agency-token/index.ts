// Supabase Edge Function to get the GHL Agency Token
// This function returns the GHL Agency Token for use in client-side code

// Setup type definitions for built-in Supabase Runtime APIs
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

serve(async (req: Request) => {
  // CORS headers - Allow specific origins for better security
  const headers = {
    'Access-Control-Allow-Origin': req.headers.get('origin') || '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Access-Control-Allow-Credentials': 'true',
    'Content-Type': 'application/json'
  };

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers });
  }

  try {
    // Get GHL credentials from environment variables
    // @ts-ignore
    const agencyToken = Deno.env.get('GHL_AGENCY_TOKEN');
    // @ts-ignore
    const companyId = Deno.env.get('GHL_COMPANY_ID');

    if (!agencyToken) {
      throw new Error('GHL Agency Token not found in environment variables');
    }

    // Return the credentials
    return new Response(
      JSON.stringify({
        agencyToken: agencyToken,
        companyId: companyId || '',
      }),
      {
        status: 200,
        headers
      }
    );
  } catch (error) {
    console.error('Error getting GHL credentials:', error);

    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers
      }
    );
  }
});
