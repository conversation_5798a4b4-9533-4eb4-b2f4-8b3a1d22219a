
// Setup type definitions for built-in Supabase Runtime APIs
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// GHL API base URL
const GHL_API_URL = 'https://services.leadconnectorhq.com';

serve(async (req: Request) => {
  // CORS headers - Allow both local development and production domains
  const headers = {
    'Access-Control-Allow-Origin': req.headers.get('origin') || '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Access-Control-Allow-Credentials': 'true',
    'Content-Type': 'application/json'
  };

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers });
  }

  try {
    // Parse the request body to get user data
    // @ts-ignore
    const userData = await req.json();
    const { email, companyName } = userData;

    if (!email) {
      throw new Error('Email is required');
    }

    // Get GHL credentials from environment variables
    // @ts-ignore
    const clientId = Deno.env.get('GHL_CLIENT_ID');
    // @ts-ignore
    const clientSecret = Deno.env.get('GHL_CLIENT_SECRET');

    if (!clientId || !clientSecret) {
      throw new Error('GHL credentials not found in environment variables');
    }

    // Step 1: Get an access token
    const tokenResponse = await fetch(`${GHL_API_URL}/oauth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: clientId,
        client_secret: clientSecret,
        grant_type: 'client_credentials',
      }),
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      throw new Error(`Failed to get GHL access token: ${errorText}`);
    }

    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;

    // Step 2: Create a subaccount
    const createResponse = await fetch(`${GHL_API_URL}/api/v1/locations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
        name: companyName || email.split('@')[0],
        email: email,
        phone: '',
        address: {},
        companyName: companyName || email.split('@')[0],
        timezone: 'America/New_York', // Default timezone
      }),
    });

    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      throw new Error(`Failed to create GHL subaccount: ${errorText}`);
    }

    const locationData = await createResponse.json();

    return new Response(JSON.stringify(locationData), {
      status: 200,
      headers
    });
  } catch (error) {
    console.error('Error creating GHL subaccount:', error);

    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers
    });
  }
});