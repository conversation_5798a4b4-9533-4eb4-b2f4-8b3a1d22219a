
// Setup type definitions for built-in Supabase Runtime APIs
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// GHL API base URL
const GHL_API_URL = 'https://services.leadconnectorhq.com';

serve(async (req: Request) => {
  // CORS headers - Allow both local development and production domains
  const headers = {
    'Access-Control-Allow-Origin': '*', // Allow all origins for testing
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers });
  }

  try {
    // Parse the request body to get user data
    // @ts-ignore
    const userData = await req.json();
    const {
      email,
      firstName,
      lastName,
      companyName,
      password,
      city,
      state,
      country,
      phone,
      role,
      company_size,
      referral_source
    } = userData;

    if (!email) {
      throw new Error('Email is required');
    }

    // Get GHL credentials from environment variables
    // For Agency access, we need the Agency Bearer Token and Company ID
    // @ts-ignore
    const agencyToken = Deno.env.get('GHL_AGENCY_TOKEN');
    // @ts-ignore
    const companyId = Deno.env.get('GHL_COMPANY_ID');

    if (!agencyToken) {
      throw new Error('GHL Agency Token not found in environment variables');
    }

    if (!companyId) {
      throw new Error('GHL_COMPANY_ID not found in environment variables');
    }

    // Use the Agency Bearer Token for authentication
    const accessToken = agencyToken;

    console.log('Using Agency Bearer Token for authentication');

    // Log the country value for debugging
    console.log('Country value received:', country);

    // Ensure country is properly formatted for GHL API
    // GHL API requires 2-letter country code or full country name
    let formattedCountry = country || '';

    // If country is provided but not in the correct format, try to convert it
    if (formattedCountry && formattedCountry.length > 2) {
      // Keep the full country name as is
      console.log('Using full country name:', formattedCountry);
    } else if (formattedCountry && formattedCountry.length === 2) {
      // Already a 2-letter code, keep as is
      console.log('Using country code:', formattedCountry);
    } else {
      // Default to US only if no country is provided
      formattedCountry = 'US';
      console.log('No country provided, defaulting to:', formattedCountry);
    }

    // Step 2: Create a subaccount/location
    const createLocationResponse = await fetch(`${GHL_API_URL}/api/v1/locations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        'Version': '2021-07-28'
      },
      body: JSON.stringify({
        name: companyName || `${firstName}'s Business`,
        email: email,
        companyId: companyId,
        timezone: 'America/New_York',
        address1: '',
        city: city || '',
        state: state || '',
        country: formattedCountry,
        phone: phone || '',
        // Store additional metadata as custom fields
        customFields: [
          { key: 'role', value: role || '' },
          { key: 'company_size', value: company_size || '' },
          { key: 'referral_source', value: referral_source || '' }
        ]
      }),
    });

    if (!createLocationResponse.ok) {
      const errorText = await createLocationResponse.text();
      throw new Error(`Failed to create GHL location: ${errorText}`);
    }

    const locationData = await createLocationResponse.json();
    const locationId = locationData.id;

    if (!locationId) {
      throw new Error('Location ID not found in GHL response');
    }

    // Step 3: Create a user account for this location
    // Using the endpoint from the documentation: POST /users
    const createUserResponse = await fetch(`${GHL_API_URL}/api/v1/users/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        'Version': '2021-07-28'
      },
      body: JSON.stringify({
        firstName: firstName,
        lastName: lastName,
        email: email,
        password: password,
        type: 'account',
        role: 'admin',
        locationIds: [locationId],
        // Store user metadata
        meta: {
          userRole: role || '',
          companySize: company_size || '',
          referralSource: referral_source || ''
        },
        permissions: {
          campaignsEnabled: true,
          contactsEnabled: true,
          workflowsEnabled: true,
          triggersEnabled: true,
          funnelsEnabled: true,
          opportunitiesEnabled: true,
          dashboardStatsEnabled: true,
          settingsEnabled: true,
          tagsEnabled: true,
          marketingEnabled: true,
          conversationsEnabled: true
        }
      }),
    });

    if (!createUserResponse.ok) {
      const errorText = await createUserResponse.text();
      // If user creation fails but we created the location, return partial success
      return new Response(JSON.stringify({
        locationId: locationId,
        error: `User creation failed: ${errorText}`
      }), {
        status: 207,
        headers
      });
    }

    // @ts-ignore
    const userData = await createUserResponse.json();

    // Return both IDs and additional data for storage in Supabase
    return new Response(JSON.stringify({
      locationId: locationId,
      userId: userData.id,
      role: role || '',
      company_size: company_size || '',
      referral_source: referral_source || '',
      message: 'GHL location and user created successfully'
    }), {
      status: 200,
      headers
    });
  } catch (error) {
    console.error('Error creating GHL account:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers
    });
  }
});
