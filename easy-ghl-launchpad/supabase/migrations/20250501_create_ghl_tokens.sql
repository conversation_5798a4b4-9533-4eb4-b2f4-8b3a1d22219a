-- Create table for storing GHL OAuth tokens
CREATE TABLE IF NOT EXISTS public.ghl_tokens (
  id BIGINT PRIMARY KEY,
  access_token TEXT NOT NULL,
  refresh_token TEXT NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  token_type TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add comment to table
COMMENT ON TABLE public.ghl_tokens IS 'Stores GoHighLevel OAuth tokens for API access';

-- Enable RLS but allow all operations for authenticated users
ALTER TABLE public.ghl_tokens ENABLE ROW LEVEL SECURITY;

-- Create policy to allow service role to manage tokens
CREATE POLICY "Service role can manage tokens" 
  ON public.ghl_tokens 
  USING (auth.role() = 'service_role');

-- Create policy to allow authenticated users to read tokens
CREATE POLICY "Authenticated users can read tokens" 
  ON public.ghl_tokens 
  FOR SELECT
  USING (auth.role() = 'authenticated');
