{"name": "Aha Sign up workflow 2", "nodes": [{"parameters": {"httpMethod": "POST", "path": "aha-signup", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [0, 0], "id": "f9c00585-ca7f-4275-8f80-301055da<PERSON>ce", "name": "Form Submission Webhook", "webhookId": "cf043875-6edf-49d7-9eef-48a0ce9ce9dc"}, {"parameters": {"method": "POST", "url": "https://services.leadconnectorhq.com/locations/", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer pit-ef4e9f36-a6db-480e-a455-e4f305a56735"}, {"name": "Version", "value": "2021-07-28"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"name\": \"{{ $json.body.body.company_name }}\",\n  \"phone\": \"{{ $json.body.body.phone }}\",\n  \"companyId\": \"gPkTndcx94O3r573TOMx\",\n  \"address\": \"{{ $json.body.body.location.address }}\",\n  \"city\": {{ $json.body.body.location.city }}\"\",\n  \"state\": \"{{ $json.body.body.location.state }}\",\n  \"country\": \"{{ $json.body.body.location.country }}\",\n  \"postalCode\": \"{{ $json.body.body.location.country }}\",\n  \"website\": \"\",\n  \"timezone\": \"US/Central\",\n  \"email\": \"{{ $json.body.body.prospectInfo.email }}\",\n  \"prospectInfo\": {\n    \"firstName\": \"{{ $json.body.body.prospectInfo.firstName }}\",\n    \"lastName\": \"{{ $json.body.body.prospectInfo.lastName }}\",\n    \"email\": \"{{ $json.body.body.prospectInfo.email }}\"\n  },\n  \"settings\": {\n    \"allowDuplicateContact\": false,\n    \"allowDuplicateOpportunity\": false,\n    \"allowFacebookNameMerge\": false,\n    \"disableContactTimezone\": false\n  },\n  \"social\": {\n    \"facebookUrl\": \"\",\n    \"googlePlus\": \"\",\n    \"linkedIn\": \"\",\n    \"foursquare\": \"\",\n    \"twitter\": \"\",\n    \"yelp\": \"\",\n    \"instagram\": \"\",\n    \"youtube\": \"\",\n    \"pinterest\": \"\",\n    \"blogRss\": \"\",\n    \"googlePlacesId\": \"\"\n  }\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [220, 0], "id": "60d696e0-8f6b-436a-9187-a5af8f426913", "name": "Create Sub-Account"}, {"parameters": {"method": "POST", "url": "https://services.leadconnectorhq.com/users/", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer pit-ef4e9f36-a6db-480e-a455-e4f305a56735"}, {"name": "Version", "value": "2021-07-28"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"companyId\": \"{{ $json.companyId }}\",\n  \"firstName\": \"{{ $json.firstName }}\",\n  \"lastName\": \"{{$json.lastName}}\",\n  \"email\": \"{{ $json.email }}\",\n  \"password\": \"AhaDefualt*2025\",\n  \"phone\": \"{{$json.phone}}\",\n  \"type\": \"account\",\n  \"role\": \"admin\",\n  \"locationIds\": [\"{{$node[\"Create Sub-Account\"].json.id}}\"],\n  \"permissions\": {\n    \"campaignsEnabled\": true,\n    \"campaignsReadOnly\": false,\n    \"contactsEnabled\": true,\n    \"workflowsEnabled\": true,\n    \"workflowsReadOnly\": false,\n    \"triggersEnabled\": true,\n    \"funnelsEnabled\": true,\n    \"websitesEnabled\": true,\n    \"opportunitiesEnabled\": true,\n    \"dashboardStatsEnabled\": true,\n    \"bulkRequestsEnabled\": true,\n    \"appointmentsEnabled\": true,\n    \"reviewsEnabled\": true,\n    \"onlineListingsEnabled\": true,\n    \"phoneCallEnabled\": true,\n    \"conversationsEnabled\": true,\n    \"assignedDataOnly\": false,\n    \"settingsEnabled\": true,\n    \"tagsEnabled\": true,\n    \"leadValueEnabled\": true,\n    \"marketingEnabled\": true,\n    \"agentReportingEnabled\": true,\n    \"socialPlanner\": true,\n    \"bloggingEnabled\": true,\n    \"invoiceEnabled\": true,\n    \"contentAiEnabled\": true,\n    \"paymentsEnabled\": true,\n    \"communitiesEnabled\": true\n  }\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, 0], "id": "294680fb-3890-4b6d-acb1-c40e21276bc9", "name": "Create User"}, {"parameters": {"method": "POST", "url": "=https://services.leadconnectorhq.com/saas-api/public-api/enable-saas/{{$node[\"Create Sub-Account\"].json.id}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer pit-ef4e9f36-a6db-480e-a455-e4f305a56735"}, {"name": "Version", "value": "2021-04-15"}, {"name": "channel", "value": "OAUTH"}, {"name": "source", "value": "INTEGRATION"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"companyId\": \"gPkTndcx94O3r573TOMx\",\n  \"isSaaSV2\": true,\n  \"contactId\": \"{{$node[\"Create Sub-Account\"].json.id}}\",\n  \"description\": \"AHA Innovations SaaS Subscription\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [660, 0], "id": "b680a437-984b-4124-8136-36eacb442cf1", "name": "Enable Saa<PERSON>"}], "pinData": {}, "connections": {"Form Submission Webhook": {"main": [[{"node": "Create Sub-Account", "type": "main", "index": 0}]]}, "Create Sub-Account": {"main": [[{"node": "Create User", "type": "main", "index": 0}]]}, "Create User": {"main": [[{"node": "Enable Saa<PERSON>", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "9e4d9ef7-8833-44a2-8712-62def7953acb", "meta": {"instanceId": "3ef132fb5a3a929de68c456f97f5fc22e5795f5a9dd9ca52fa77117b5a77a423"}, "id": "JWnYY5NzBso1v3Kr", "tags": []}