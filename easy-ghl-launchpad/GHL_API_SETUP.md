# GoHighLevel API Integration Setup

This document provides instructions on how to set up the GoHighLevel API integration for AHA-Innovations.

## Prerequisites

1. A GoHighLevel Agency account
2. Access to the Supabase project

## Setting Up Environment Variables

You need to set the following environment variables in your Supabase Edge Functions:

1. `GHL_AGENCY_TOKEN`: Your GoHighLevel Agency Bearer Token
2. `GHL_COMPANY_ID`: Your GoHighLevel Company ID
3. `GHL_CLIENT_ID`: Your GoHighLevel OAuth Client ID (for OAuth flow)
4. `GHL_CLIENT_SECRET`: Your GoHighLevel OAuth Client Secret (for OAuth flow)

### How to Get Your GoHighLevel Agency Bearer Token

1. Log in to your GoHighLevel Agency account
2. Go to Settings > API & Integrations > Agency API
3. Generate a new API Key or use an existing one
4. The Bearer Token is the API Key prefixed with "Bearer "

Example: If your API Key is `abc123`, your Bearer Token is `Bearer abc123`

### How to Get Your GoHighLevel Company ID

1. Log in to your GoHighLevel Agency account
2. Go to Settings > Agency Settings
3. Your Company ID is displayed on this page

## Setting Up Supabase Edge Functions

1. Navigate to your Supabase project
2. Go to Edge Functions
3. Set the environment variables for each function:

```bash
supabase secrets set GHL_AGENCY_TOKEN="Bearer your_api_key_here"
supabase secrets set GHL_COMPANY_ID="your_company_id_here"
supabase secrets set GHL_CLIENT_ID="your_oauth_client_id_here"
supabase secrets set GHL_CLIENT_SECRET="your_oauth_client_secret_here"
```

## Testing the Integration

1. Make sure your local development environment is set up
2. Update the `mockGHLCredentials.ts` file with your test credentials
3. Run the application locally
4. Test the onboarding flow to ensure it creates GHL accounts correctly

## Troubleshooting

### CORS Issues

If you encounter CORS issues, make sure:

1. The `Access-Control-Allow-Origin` header is set correctly in your Edge Functions
2. The `Access-Control-Allow-Credentials` header is set to `true`
3. The `Access-Control-Allow-Headers` includes all necessary headers

### API Authentication Issues

If you encounter authentication issues:

1. Verify that your Agency Bearer Token is valid and not expired
2. Make sure you're using the correct Company ID
3. Check that the API endpoints are correct according to the GHL API documentation

## API Endpoints Reference

### Get Sub-Account (Location)

```
GET https://services.leadconnectorhq.com/locations/{locationId}
```

### Create Sub-Account (Location)

```
POST https://services.leadconnectorhq.com/api/v1/locations/
```

### Create User

```
POST https://services.leadconnectorhq.com/api/v1/users/
```

### Search Contacts

```
GET https://services.leadconnectorhq.com/api/v1/contacts/lookup?email={email}
```

For more details, refer to the [GoHighLevel API Documentation](https://developers.gohighlevel.com/).
