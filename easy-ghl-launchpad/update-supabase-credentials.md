# Update Supabase Credentials

After creating your new Supabase project, you need to update the following files with your new credentials:

## 1. src/utils/ghlPrivateIntegration.ts

```typescript
// Supabase credentials
const supabaseUrl = 'YOUR_NEW_SUPABASE_URL';
const supabaseAnonKey = 'YOUR_NEW_SUPABASE_ANON_KEY';
```

## 2. src/utils/ghlClient.ts

```typescript
// Supabase credentials
const supabaseUrl = 'YOUR_NEW_SUPABASE_URL';
const supabaseAnonKey = 'YOUR_NEW_SUPABASE_ANON_KEY';
```

## 3. src/utils/ghlTokenManager.ts

```typescript
// Supabase credentials
const supabaseUrl = 'YOUR_NEW_SUPABASE_URL';
const supabaseAnonKey = 'YOUR_NEW_SUPABASE_ANON_KEY';
```

## 4. Any other files that might contain Supabase credentials

Search for `fpratwslcktwpzlbzlhm.supabase.co` in your codebase to find any other files that might need updating.

## 5. Environment Variables in Vercel

Don't forget to update your environment variables in Vercel:

1. Go to your Vercel project
2. Go to Settings → Environment Variables
3. Update any Supabase-related environment variables with your new credentials
